import React from "react";
import { <PERSON> } from "react-router-dom";
import { Link as HLink } from "@heroui/react";
import {
  ArrowRight,
  Users,
  Award,
  BookOpen,
  Mail,
  Phone,
  GraduationCap,
  Trophy,
} from "lucide-react";
import WzwLogo from "@/assets/wzw-logo.png";
import WzwBook from "@/assets/wzw-book.png";
import PkuHome from "@/assets/pku-home.png";

const About: React.FC = () => {
  // 团队成员数据
  const teamMembers = [
    {
      name: "文再文",
      position: "中心主任",
      avatar: WzwLogo,
    },
    {
      name: "张三",
      position: "副主任",
      avatar:
        "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=300",
    },
    {
      name: "李四",
      position: "研究员",
      avatar:
        "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=300",
    },
    {
      name: "王五",
      position: "研究员",
      avatar:
        "https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=300",
    },
    {
      name: "赵六",
      position: "研究员",
      avatar:
        "https://images.pexels.com/photos/3184160/pexels-photo-3184160.jpeg?auto=compress&cs=tinysrgb&w=300",
    },
    {
      name: "钱七",
      position: "研究员",
      avatar:
        "https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg?auto=compress&cs=tinysrgb&w=300",
    },
  ];

  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div className="pt-10">
              <h1 className="text-4xl lg:text-5xl font-bold mb-4">
                北京大学长沙计算与数字经济研究院
              </h1>
              <p className="text-xl text-secondary-200 mb-6">
                北大所能，产业所需
              </p>
              <div className="text-base text-gray-200 mb-8 leading-relaxed space-y-4">
                <p>
                  北京大学长沙计算与数字经济研究院是由北京大学、长沙市人民政府和湖南湘江新区（长沙高新区）管理委员会共建的长沙市属事业单位。
                  聘请了张平文院士和陈松蹊院士担任首席科学家，引进了包含4名长江学者特聘教授和2名万人计划领军人才在内的40余名高端人才，
                  现有员工186人，其中全职员工144人，博士43人，硕士及以上学历占比77.96%。
                </p>
                <p>
                  组建了由一个智库加多个研究中心组成的"1+N"创新体系，重点突破先进计算、数字经济等领域"卡脖子"技术难题。
                  已获批国家自然科学基金依托单位、国家级博士后科研工作站和湖南省工程研究中心等10余项国家级、省部级平台资质。
                </p>
              </div>

              <div className="flex flex-col sm:flex-row gap-4">
                <Link
                  to="/contact"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-white text-theme-900 font-medium rounded-lg hover:bg-gray-100 transition-all duration-300 shadow-lg"
                >
                  <span>研究院详情</span>
                  <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 右侧图片 */}
            <div className="relative">
              <div className="relative h-80 lg:h-96 rounded-2xl overflow-hidden shadow-2xl">
                <img
                  src={PkuHome}
                  alt="北京大学长沙计算与数字经济研究院"
                  className="w-full h-full object-cover"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 工业智能中心介绍 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              工业智能研究中心
            </h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-4xl mx-auto leading-relaxed">
              工业智能研究中心是北京大学长沙计算与数字经济研究院下设重要研究中心之一，依托北京大学国际数学研究中心文再文教授及其科研团队的深厚研究根基，
              聚焦人工智能在产业中驱动决策的智能化发展，构建基于运筹大模型底座的智能决策建模平台+国产自主可控数学规划求解器为核心支撑的新一代智能决策建模平台，
              全方位提升生产效率，强力推动企业实现可持续的长远发展。
            </p>
          </div>
        </div>
      </section>

      {/* 中心主任介绍 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              中心主任介绍
            </h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-start">
            {/* 左侧：主任信息 */}
            <div className="h-full space-y-6">
              {/* 主任头像和基本信息 */}
              <div className="bg-white rounded-2xl p-8 shadow-lg text-center">
                <div className="w-36 h-36 mx-auto mb-6 rounded-full overflow-hidden shadow-lg">
                  <img
                    src={WzwLogo}
                    alt="文再文教授"
                    className="w-full h-full object-cover"
                  />
                </div>
                <h3 className="text-2xl font-bold text-gray-900 mb-2">
                  <HLink
                    href="http://faculty.bicmr.pku.edu.cn/~wenzw/"
                    target="_blank"
                    className="text-gray-900 text-2xl font-bold underline"
                  >
                    文再文
                  </HLink>{" "}
                  教授
                </h3>
                <p className="text-theme-600 font-medium mb-4">中心主任</p>

                <div className="space-y-4 text-left">
                  <div className="flex items-center text-gray-600">
                    <GraduationCap className="h-4 w-4 mr-2 text-theme-600" />
                    <span className="text-sm">北京大学 博雅特聘教授</span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <GraduationCap className="h-4 w-4 mr-2 text-theme-600" />
                    <span className="text-sm">
                      北京大学 北京国际数学研究中心 长聘教授
                    </span>
                  </div>
                  <div className="flex items-center text-gray-600">
                    <Award className="h-4 w-4 mr-2 text-theme-600" />
                    <span className="text-sm">中国运筹学会 副理事长</span>
                  </div>
                </div>
              </div>

              {/* 主要荣誉 */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <Trophy className="h-5 w-5 mr-2 text-theme-600" />
                  主要荣誉
                </h4>
                <div className="space-y-4">
                  {[
                    "教育部长江学者",
                    "万人领军",
                    "中国青年科技奖",
                    "北京杰出青年中关村奖",
                    "华为火花奖",
                  ].map((honor, index) => (
                    <div
                      key={index}
                      className="flex items-center text-gray-600"
                    >
                      <div className="w-2 h-2 bg-theme-600 rounded-full mr-3" />
                      <span className="text-sm">{honor}</span>
                    </div>
                  ))}
                </div>
              </div>
            </div>

            {/* 右侧：详细信息 */}
            <div className="h-full flex flex-col justify-between">
              {/* 研究方向 */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h4 className="text-lg font-bold text-gray-900 mb-4">
                  研究方向
                </h4>
                <div className="grid grid-cols-2 gap-3">
                  {["优化算法与理论", "机器学习", "数据科学", "人工智能"].map(
                    (direction, index) => (
                      <div
                        key={index}
                        className="bg-theme-50 rounded-lg p-3 text-center"
                      >
                        <span className="text-sm font-medium text-theme-700">
                          {direction}
                        </span>
                      </div>
                    )
                  )}
                </div>
              </div>

              {/* 主任作品 */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h4 className="text-lg font-bold text-gray-900 mb-4 flex items-center">
                  <BookOpen className="h-5 w-5 mr-2 text-theme-600" />
                  主要著作
                </h4>
                <div className="flex items-start space-x-4">
                  <div className="w-28 rounded-lg flex items-center justify-center shadow-md">
                    <img
                      src={WzwBook}
                      alt="文再文教授著作"
                      className="w-full h-full object-contain"
                    />
                  </div>
                  <div className="flex-1">
                    <h5 className="font-semibold text-gray-900 mb-2">
                      《最优化：模型、算法和理论》
                    </h5>
                    <h5 className="font-semibold text-gray-900 mb-2">
                      《最优化计算方法》
                    </h5>
                    <div className="space-y-1 text-sm text-gray-600">
                      <p>• 累计印刷5次2.2万余本</p>
                      <p>• 得到了北大、复旦、上海交大等超百所高校采用</p>
                      <p>• 高等教育出版社纸质版、作者版、电子版</p>
                      <p>• 中文版和英文版电子课件</p>
                      <p>• 教材习题答案教师版、学生版</p>
                      <p>• 教材配套程序和网页展示</p>
                    </div>
                  </div>
                </div>
              </div>

              {/* 学术软件 */}
              <div className="bg-white rounded-2xl p-6 shadow-lg">
                <h4 className="text-lg font-bold text-gray-900 mb-4">
                  学术软件
                </h4>
                <div className="space-y-3">
                  <div className="flex flex-wrap gap-2">
                    {[
                      "OptM",
                      "LMaFit",
                      "SSNSDP",
                      "ARNT",
                      "LMSVD",
                      "SENG",
                      "NG+等",
                    ].map((software, index) => (
                      <span
                        key={index}
                        className="px-3 py-1 bg-gray-100 text-gray-700 rounded-full text-sm font-medium"
                      >
                        {software}
                      </span>
                    ))}
                  </div>
                  <p className="text-sm text-gray-600 mt-3">
                    半光滑牛顿法被阿里达摩院和华为部分采用求解锥规划
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 团队成员介绍 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">团队成员</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              汇聚优秀人才，共同推进工业智能技术发展
            </p>
          </div>

          <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-6">
            {teamMembers.map((member, index) => (
              <div key={index} className="text-center group">
                <div className="w-24 h-24 mx-auto mb-4 rounded-full overflow-hidden shadow-lg group-hover:shadow-xl transition-all duration-300">
                  <img
                    src={member.avatar}
                    alt={member.name}
                    className="w-full h-full object-cover group-hover:scale-110 transition-transform duration-300"
                  />
                </div>
                <h3 className="text-sm font-semibold text-gray-900 mb-1">
                  {member.name}
                </h3>
                <p className="text-xs text-theme-600">{member.position}</p>
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* 联系我们 */}
      <section className="py-16">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center">
          <h2 className="text-3xl font-bold text-gray-900 mb-4">联系我们</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              欢迎与我们联系，了解更多关于工业智能研究中心的信息
            </p>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-8 max-w-2xl mx-auto">
            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-xl mb-4">
                <Mail className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">邮箱联系</h3>
              <p className="text-gray-700"><EMAIL></p>
            </div>

            <div className="bg-white/10 backdrop-blur-sm rounded-2xl p-6 border border-white/20">
              <div className="inline-flex items-center justify-center w-12 h-12 bg-white/20 rounded-xl mb-4">
                <Phone className="h-6 w-6 text-white" />
              </div>
              <h3 className="text-lg font-semibold mb-2">电话联系</h3>
              <p className="text-gray-700">0731-81877778</p>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default About;
