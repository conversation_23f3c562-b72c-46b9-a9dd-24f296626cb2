@tailwind base;
@tailwind components;
@tailwind utilities;

/* 默认红色主题变量 */
:root {
  /* 默认红色主题 */
  --primary-color: #790606;
  --primary-light: #B91C1C;
  --primary-dark: #5A0404;
  --accent-color: #C5AA89;
  --accent-light: #D4BFA0;
  --accent-dark: #A08B6F;
  
  /* 副主题色 (红色主题下为金色) */
  --secondary-color: #C5AA89;
  --secondary-light: #D4BFA0;
  --secondary-dark: #A08B6F;
  
  /* RGB值用于阴影 */
  --primary-rgb: 121, 6, 6;
  --accent-rgb: 197, 170, 137;
  --secondary-rgb: 197, 170, 137;
  
  /* 主题色阶 */
  --theme-50: #FEF2F2;
  --theme-100: #FEE2E2;
  --theme-200: #FECACA;
  --theme-300: #FCA5A5;
  --theme-400: #F87171;
  --theme-500: #EF4444;
  --theme-600: #DC2626;
  --theme-700: #B91C1C;
  --theme-800: #991B1B;
  --theme-900: #790606;
  --theme-950: #5A0404;
  
  /* 副主题色阶 (金色) */
  --secondary-50: #FEFDF8;
  --secondary-100: #FDF9F0;
  --secondary-200: #F9F0E1;
  --secondary-300: #F3E6D2;
  --secondary-400: #EDDCC3;
  --secondary-500: #E7D2B4;
  --secondary-600: #D4BFA0;
  --secondary-700: #C5AA89;
  --secondary-800: #A08B6F;
  --secondary-900: #7B6B55;

  --warm-gray: #F5F3F0;
}

/* 蓝色主题变量 */
[data-theme="blue"] {
  --primary-color: #2563eb;
  --primary-light: #3b82f6;
  --primary-dark: #1d4ed8;
  --accent-color: #64748b;
  --accent-light: #94a3b8;
  --accent-dark: #475569;
  
  /* 副主题色 (蓝色主题下为浅蓝/灰蓝色) */
  --secondary-color: #64748b;
  --secondary-light: #94a3b8;
  --secondary-dark: #475569;
  
  /* RGB值用于阴影 */
  --primary-rgb: 37, 99, 235;
  --accent-rgb: 100, 116, 139;
  --secondary-rgb: 100, 116, 139;
  
  /* 主题色阶 */
  --theme-50: #EFF6FF;
  --theme-100: #DBEAFE;
  --theme-200: #BFDBFE;
  --theme-300: #93C5FD;
  --theme-400: #60A5FA;
  --theme-500: #3B82F6;
  --theme-600: #2563EB;
  --theme-700: #1D4ED8;
  --theme-800: #1E40AF;
  --theme-900: #1E3A8A;
  --theme-950: #172554;
  
  /* 副主题色阶 (灰蓝色) */
  --secondary-50: #F8FAFC;
  --secondary-100: #F1F5F9;
  --secondary-200: #E2E8F0;
  --secondary-300: #CBD5E1;
  --secondary-400: #94A3B8;
  --secondary-500: #64748B;
  --secondary-600: #475569;
  --secondary-700: #334155;
  --secondary-800: #1E293B;
  --secondary-900: #0F172A;
}

/* 主题色阶工具类 */
@layer utilities {
  .bg-theme-50 { background-color: var(--theme-50); }
  .bg-theme-100 { background-color: var(--theme-100); }
  .bg-theme-200 { background-color: var(--theme-200); }
  .bg-theme-300 { background-color: var(--theme-300); }
  .bg-theme-400 { background-color: var(--theme-400); }
  .bg-theme-500 { background-color: var(--theme-500); }
  .bg-theme-600 { background-color: var(--theme-600); }
  .bg-theme-700 { background-color: var(--theme-700); }
  .bg-theme-800 { background-color: var(--theme-800); }
  .bg-theme-900 { background-color: var(--theme-900); }
  .bg-theme-950 { background-color: var(--theme-950); }
  
  .text-theme-50 { color: var(--theme-50); }
  .text-theme-100 { color: var(--theme-100); }
  .text-theme-200 { color: var(--theme-200); }
  .text-theme-300 { color: var(--theme-300); }
  .text-theme-400 { color: var(--theme-400); }
  .text-theme-500 { color: var(--theme-500); }
  .text-theme-600 { color: var(--theme-600); }
  .text-theme-700 { color: var(--theme-700); }
  .text-theme-800 { color: var(--theme-800); }
  .text-theme-900 { color: var(--theme-900); }
  .text-theme-950 { color: var(--theme-950); }
  
  .border-theme-50 { border-color: var(--theme-50); }
  .border-theme-100 { border-color: var(--theme-100); }
  .border-theme-200 { border-color: var(--theme-200); }
  .border-theme-300 { border-color: var(--theme-300); }
  .border-theme-400 { border-color: var(--theme-400); }
  .border-theme-500 { border-color: var(--theme-500); }
  .border-theme-600 { border-color: var(--theme-600); }
  .border-theme-700 { border-color: var(--theme-700); }
  .border-theme-800 { border-color: var(--theme-800); }
  .border-theme-900 { border-color: var(--theme-900); }
  .border-theme-950 { border-color: var(--theme-950); }
  
  /* Hover状态 */
  .hover\:bg-theme-50:hover { background-color: var(--theme-50); }
  .hover\:bg-theme-100:hover { background-color: var(--theme-100); }
  .hover\:bg-theme-200:hover { background-color: var(--theme-200); }
  .hover\:bg-theme-300:hover { background-color: var(--theme-300); }
  .hover\:bg-theme-400:hover { background-color: var(--theme-400); }
  .hover\:bg-theme-500:hover { background-color: var(--theme-500); }
  .hover\:bg-theme-600:hover { background-color: var(--theme-600); }
  .hover\:bg-theme-700:hover { background-color: var(--theme-700); }
  .hover\:bg-theme-800:hover { background-color: var(--theme-800); }
  .hover\:bg-theme-900:hover { background-color: var(--theme-900); }
  
  .hover\:text-theme-50:hover { color: var(--theme-50); }
  .hover\:text-theme-100:hover { color: var(--theme-100); }
  .hover\:text-theme-200:hover { color: var(--theme-200); }
  .hover\:text-theme-300:hover { color: var(--theme-300); }
  .hover\:text-theme-400:hover { color: var(--theme-400); }
  .hover\:text-theme-500:hover { color: var(--theme-500); }
  .hover\:text-theme-600:hover { color: var(--theme-600); }
  .hover\:text-theme-700:hover { color: var(--theme-700); }
  .hover\:text-theme-800:hover { color: var(--theme-800); }
  .hover\:text-theme-900:hover { color: var(--theme-900); }
  
  /* 渐变相关 */
  .from-theme-50 { --tw-gradient-from: var(--theme-50); }
  .from-theme-100 { --tw-gradient-from: var(--theme-100); }
  .from-theme-200 { --tw-gradient-from: var(--theme-200); }
  .from-theme-300 { --tw-gradient-from: var(--theme-300); }
  .from-theme-400 { --tw-gradient-from: var(--theme-400); }
  .from-theme-500 { --tw-gradient-from: var(--theme-500); }
  .from-theme-600 { --tw-gradient-from: var(--theme-600); }
  .from-theme-700 { --tw-gradient-from: var(--theme-700); }
  .from-theme-800 { --tw-gradient-from: var(--theme-800); }
  .from-theme-900 { --tw-gradient-from: var(--theme-900); }

  .via-theme-50 { --tw-gradient-via: var(--theme-50); }
  .via-theme-100 { --tw-gradient-via: var(--theme-100); }
  .via-theme-200 { --tw-gradient-via: var(--theme-200); }
  .via-theme-300 { --tw-gradient-via: var(--theme-300); }
  .via-theme-400 { --tw-gradient-via: var(--theme-400); }
  .via-theme-500 { --tw-gradient-via: var(--theme-500); }
  .via-theme-600 { --tw-gradient-via: var(--theme-600); }
  .via-theme-700 { --tw-gradient-via: var(--theme-700); }
  .via-theme-800 { --tw-gradient-via: var(--theme-800); }
  .via-theme-900 { --tw-gradient-via: var(--theme-900); }
  
  .to-theme-50 { --tw-gradient-to: var(--theme-50); }
  .to-theme-100 { --tw-gradient-to: var(--theme-100); }
  .to-theme-200 { --tw-gradient-to: var(--theme-200); }
  .to-theme-300 { --tw-gradient-to: var(--theme-300); }
  .to-theme-400 { --tw-gradient-to: var(--theme-400); }
  .to-theme-500 { --tw-gradient-to: var(--theme-500); }
  .to-theme-600 { --tw-gradient-to: var(--theme-600); }
  .to-theme-700 { --tw-gradient-to: var(--theme-700); }
  .to-theme-800 { --tw-gradient-to: var(--theme-800); }
  .to-theme-900 { --tw-gradient-to: var(--theme-900); }

  .bg-secondary-50 { background-color: var(--secondary-50); }
  .bg-secondary-100 { background-color: var(--secondary-100); }
  .bg-secondary-200 { background-color: var(--secondary-200); }
  .bg-secondary-300 { background-color: var(--secondary-300); }
  .bg-secondary-400 { background-color: var(--secondary-400); }
  .bg-secondary-500 { background-color: var(--secondary-500); }
  .bg-secondary-600 { background-color: var(--secondary-600); }
  .bg-secondary-700 { background-color: var(--secondary-700); }
  .bg-secondary-800 { background-color: var(--secondary-800); }
  .bg-secondary-900 { background-color: var(--secondary-900); }
  .bg-secondary-950 { background-color: var(--secondary-950); }
  
  .text-secondary-50 { color: var(--secondary-50); }
  .text-secondary-100 { color: var(--secondary-100); }
  .text-secondary-200 { color: var(--secondary-200); }
  .text-secondary-300 { color: var(--secondary-300); }
  .text-secondary-400 { color: var(--secondary-400); }
  .text-secondary-500 { color: var(--secondary-500); }
  .text-secondary-600 { color: var(--secondary-600); }
  .text-secondary-700 { color: var(--secondary-700); }
  .text-secondary-800 { color: var(--secondary-800); }
  .text-secondary-900 { color: var(--secondary-900); }
  .text-secondary-950 { color: var(--secondary-950); }
  
  .border-secondary-50 { border-color: var(--secondary-50); }
  .border-secondary-100 { border-color: var(--secondary-100); }
  .border-secondary-200 { border-color: var(--secondary-200); }
  .border-secondary-300 { border-color: var(--secondary-300); }
  .border-secondary-400 { border-color: var(--secondary-400); }
  .border-secondary-500 { border-color: var(--secondary-500); }
  .border-secondary-600 { border-color: var(--secondary-600); }
  .border-secondary-700 { border-color: var(--secondary-700); }
  .border-secondary-800 { border-color: var(--secondary-800); }
  .border-secondary-900 { border-color: var(--secondary-900); }
  .border-secondary-950 { border-color: var(--secondary-950); }
  
  /* Hover状态 */
  .hover\:bg-secondary-50:hover { background-color: var(--secondary-50); }
  .hover\:bg-secondary-100:hover { background-color: var(--secondary-100); }
  .hover\:bg-secondary-200:hover { background-color: var(--secondary-200); }
  .hover\:bg-secondary-300:hover { background-color: var(--secondary-300); }
  .hover\:bg-secondary-400:hover { background-color: var(--secondary-400); }
  .hover\:bg-secondary-500:hover { background-color: var(--secondary-500); }
  .hover\:bg-secondary-600:hover { background-color: var(--secondary-600); }
  .hover\:bg-secondary-700:hover { background-color: var(--secondary-700); }
  .hover\:bg-secondary-800:hover { background-color: var(--secondary-800); }
  .hover\:bg-secondary-900:hover { background-color: var(--secondary-900); }
  
  .hover\:text-secondary-50:hover { color: var(--secondary-50); }
  .hover\:text-secondary-100:hover { color: var(--secondary-100); }
  .hover\:text-secondary-200:hover { color: var(--secondary-200); }
  .hover\:text-secondary-300:hover { color: var(--secondary-300); }
  .hover\:text-secondary-400:hover { color: var(--secondary-400); }
  .hover\:text-secondary-500:hover { color: var(--secondary-500); }
  .hover\:text-secondary-600:hover { color: var(--secondary-600); }
  .hover\:text-secondary-700:hover { color: var(--secondary-700); }
  .hover\:text-secondary-800:hover { color: var(--secondary-800); }
  .hover\:text-secondary-900:hover { color: var(--secondary-900); }
  
  /* 渐变相关 */
  .from-secondary-50 { --tw-gradient-from: var(--secondary-50); }
  .from-secondary-100 { --tw-gradient-from: var(--secondary-100); }
  .from-secondary-200 { --tw-gradient-from: var(--secondary-200); }
  .from-secondary-300 { --tw-gradient-from: var(--secondary-300); }
  .from-secondary-400 { --tw-gradient-from: var(--secondary-400); }
  .from-secondary-500 { --tw-gradient-from: var(--secondary-500); }
  .from-secondary-600 { --tw-gradient-from: var(--secondary-600); }
  .from-secondary-700 { --tw-gradient-from: var(--secondary-700); }
  .from-secondary-800 { --tw-gradient-from: var(--secondary-800); }
  .from-secondary-900 { --tw-gradient-from: var(--secondary-900); }

  .via-secondary-50 { --tw-gradient-via: var(--secondary-50); }
  .via-secondary-100 { --tw-gradient-via: var(--secondary-100); }
  .via-secondary-200 { --tw-gradient-via: var(--secondary-200); }
  .via-secondary-300 { --tw-gradient-via: var(--secondary-300); }
  .via-secondary-400 { --tw-gradient-via: var(--secondary-400); }
  .via-secondary-500 { --tw-gradient-via: var(--secondary-500); }
  .via-secondary-600 { --tw-gradient-via: var(--secondary-600); }
  .via-secondary-700 { --tw-gradient-via: var(--secondary-700); }
  .via-secondary-800 { --tw-gradient-via: var(--secondary-800); }
  .via-secondary-900 { --tw-gradient-via: var(--secondary-900); }
  
  .to-secondary-50 { --tw-gradient-to: var(--secondary-50); }
  .to-secondary-100 { --tw-gradient-to: var(--secondary-100); }
  .to-secondary-200 { --tw-gradient-to: var(--secondary-200); }
  .to-secondary-300 { --tw-gradient-to: var(--secondary-300); }
  .to-secondary-400 { --tw-gradient-to: var(--secondary-400); }
  .to-secondary-500 { --tw-gradient-to: var(--secondary-500); }
  .to-secondary-600 { --tw-gradient-to: var(--secondary-600); }
  .to-secondary-700 { --tw-gradient-to: var(--secondary-700); }
  .to-secondary-800 { --tw-gradient-to: var(--secondary-800); }
  .to-secondary-900 { --tw-gradient-to: var(--secondary-900); }
}

/* 主题过渡效果 */
.theme-transition {
  transition: background-color 0.3s ease, color 0.3s ease, border-color 0.3s ease, box-shadow 0.3s ease;
}

/* Enhanced animations */
@keyframes float {
  0%, 100% { transform: translateY(0px); }
  50% { transform: translateY(-20px); }
}

@keyframes pulse-glow {
  0%, 100% { 
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
  }
  50% { 
    box-shadow: 0 0 40px rgba(var(--primary-rgb), 0.6);
  }
}

@keyframes gradient-shift {
  0% { background-position: 0% 50%; }
  50% { background-position: 100% 50%; }
  100% { background-position: 0% 50%; }
}

@keyframes scroll {
  0% { transform: translateX(0); }
  100% { transform: translateX(-50%); }
}

@keyframes fadeInUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInLeft {
  from {
    opacity: 0;
    transform: translateX(-50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(50px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.8);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes rotateIn {
  from {
    opacity: 0;
    transform: rotate(-10deg) scale(0.8);
  }
  to {
    opacity: 1;
    transform: rotate(0deg) scale(1);
  }
}

/* Animation classes */
.animate-float {
  animation: float 6s ease-in-out infinite;
}

.animate-pulse-glow {
  animation: pulse-glow 2s ease-in-out infinite;
}

.animate-gradient {
  background-size: 200% 200%;
  animation: gradient-shift 4s ease infinite;
}

.animate-scroll {
  animation: scroll 30s linear infinite;
}

.animate-fade-in-up {
  animation: fadeInUp 0.8s ease-out forwards;
}

.animate-slide-in-left {
  animation: slideInLeft 0.8s ease-out forwards;
}

.animate-slide-in-right {
  animation: slideInRight 0.8s ease-out forwards;
}

.animate-scale-in {
  animation: scaleIn 0.6s ease-out forwards;
}

.animate-rotate-in {
  animation: rotateIn 0.8s ease-out forwards;
}

/* Smooth scrolling */
html {
  scroll-behavior: smooth;
}

/* Custom scrollbar */
::-webkit-scrollbar {
  width: 8px;
}

::-webkit-scrollbar-track {
  background: var(--warm-gray);
}

::-webkit-scrollbar-thumb {
  background: var(--secondary-color);
  border-radius: 4px;
}

::-webkit-scrollbar-thumb:hover {
  background: var(--secondary-dark);
}

/* Enhanced hover effects */
.hover-lift {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.hover-lift:hover {
  transform: translateY(-8px) scale(1.02);
  box-shadow: 0 20px 40px rgba(var(--primary-rgb), 0.15);
}

.hover-glow {
  transition: all 0.3s ease;
}

.hover-glow:hover {
  box-shadow: 0 0 30px rgba(var(--secondary-rgb), 0.4);
  transform: scale(1.05);
}

.hover-rotate {
  transition: transform 0.3s ease;
}

.hover-rotate:hover {
  transform: rotate(5deg) scale(1.1);
}

/* Gradient backgrounds */
.bg-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-dark) 100%);
}

.bg-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
}

.bg-gradient-hero {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-dark) 50%, var(--secondary-color) 100%);
}

.bg-gradient-card {
  background: linear-gradient(145deg, rgba(255, 255, 255, 0.9) 0%, rgba(245, 243, 240, 0.8) 100%);
}

/* Text gradients */
.text-gradient-primary {
  background: linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

.text-gradient-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  background-clip: text;
}

/* Glass morphism effect */
.glass-effect {
  background: rgba(255, 255, 255, 0.1);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.2);
}

/* Particle animation */
.particle {
  position: absolute;
  border-radius: 50%;
  pointer-events: none;
  opacity: 0.6;
  animation: float 8s ease-in-out infinite;
}

.particle:nth-child(odd) {
  background: var(--secondary-color);
  animation-delay: -2s;
}

.particle:nth-child(even) {
  background: var(--primary-red);
  animation-delay: -4s;
}

/* Enhanced button styles */
.btn-primary {
  background: linear-gradient(135deg, var(--primary-red) 0%, var(--dark-red) 100%);
  color: white;
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
  position: relative;
  overflow: hidden;
}

.btn-primary::before {
  content: '';
  position: absolute;
  top: 0;
  left: -100%;
  width: 100%;
  height: 100%;
  background: linear-gradient(90deg, transparent, rgba(255, 255, 255, 0.2), transparent);
  transition: left 0.5s;
}

.btn-primary:hover::before {
  left: 100%;
}

.btn-primary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(var(--primary-rgb), 0.3);
}

.btn-secondary {
  background: linear-gradient(135deg, var(--secondary-color) 0%, var(--dark-gold) 100%);
  color: var(--primary-red);
  border: none;
  padding: 12px 32px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-secondary:hover {
  transform: translateY(-2px);
  box-shadow: 0 10px 25px rgba(var(--secondary-rgb), 0.3);
}

.btn-outline {
  background: transparent;
  color: var(--primary-red);
  border: 2px solid var(--primary-red);
  padding: 10px 30px;
  border-radius: 8px;
  font-weight: 600;
  transition: all 0.3s ease;
}

.btn-outline:hover {
  background: var(--primary-red);
  color: white;
  transform: translateY(-2px);
}

/* Card enhancements */
.card-enhanced {
  background: var(--primary-white);
  border-radius: 16px;
  box-shadow: 0 4px 20px rgba(var(--primary-rgb), 0.1);
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
  position: relative;
  overflow: hidden;
}

.card-enhanced::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 4px;
  background: linear-gradient(90deg, var(--primary-red) 0%, var(--secondary-color) 100%);
  transform: scaleX(0);
  transition: transform 0.3s ease;
}

.card-enhanced:hover::before {
  transform: scaleX(1);
}

.card-enhanced:hover {
  transform: translateY(-8px);
  box-shadow: 0 20px 40px rgba(var(--primary-rgb), 0.15);
}

/* Loading spinner */
.spinner {
  border: 3px solid var(--warm-gray);
  border-top: 3px solid var(--primary-red);
  border-radius: 50%;
  width: 24px;
  height: 24px;
  animation: spin 1s linear infinite;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

/* Focus states */
.focus-visible:focus {
  outline: 2px solid var(--secondary-color);
  outline-offset: 2px;
}

/* Responsive design utilities */
@media (max-width: 768px) {
  .text-responsive {
    font-size: 1.125rem;
    line-height: 1.5;
  }
  
  .card-enhanced {
    margin: 0 16px;
  }
}

/* Print styles */
@media print {
  .no-print {
    display: none !important;
  }
}

/* Intersection Observer animations */
.fade-in-section {
  opacity: 0;
  transform: translateY(20px);
  transition: all 0.6s ease-out;
}

.fade-in-section.is-visible {
  opacity: 1;
  transform: translateY(0);
}

/* Enhanced typography */
.heading-primary {
  font-size: 3rem;
  font-weight: 700;
  line-height: 1.2;
  letter-spacing: -0.02em;
}


.heading-secondary {
  font-size: clamp(2rem, 4vw, 3rem);
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.01em;
}

.text-body {
  font-size: 1.125rem;
  line-height: 1.7;
  color: var(--text-medium);
}


/* Custom utilities */
.backdrop-blur-custom {
  backdrop-filter: blur(12px);
  -webkit-backdrop-filter: blur(12px);
}

.shadow-custom {
  box-shadow: 0 10px 30px rgba(var(--primary-rgb), 0.1);
}

.shadow-custom-hover:hover {
  box-shadow: 0 20px 50px rgba(121, 6, 6, 0.15);
}


/* Parallax effect */
.parallax {
  transform: translateZ(0);
  will-change: transform;
}

/* Enhanced transitions */
.transition-custom {
  transition: all 0.4s cubic-bezier(0.175, 0.885, 0.32, 1.275);
}

.transition-smooth {
  transition: all 0.3s ease-in-out;
}






