import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import {Link as HLink} from "@heroui/react";
import { Award, Users, Globe, Lightbulb, ArrowRight, Building2 } from 'lucide-react';

const CompanyStrength: React.FC = () => {
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const strengths = [
    {
      icon: <Award className="h-6 w-6" />,
      title: '技术领先',
      description: '北大顶尖科研团队深耕运筹优化领域15余年，积累沉淀多项核心“卡脖子”技术',
      stats: '北大技术创新源动力'
    },
    {
      icon: <Users className="h-6 w-6" />,
      title: '专业团队',
      description: '汇聚顶尖算法专家和行业资深顾问',
      stats: '行业深耕落地应用团队'
    },
    {
      icon: <Globe className="h-6 w-6" />,
      title: '机制创新',
      description: '政府支持新型研发机构孵化，政策导向，管理体制创新，打通成果转化“最后一公里”',
      stats: '服务地方所需，展现北大所能 '
    },
    {
      icon: <Lightbulb className="h-8 w-8" />,
      title: '生态链接',
      description: '链接高校、企业、科研机构、社会团体等生态资源，推动产学研健康发展',
      stats: '打造开源生态社区，推动产学研发展',
      color: 'from-secondary-700 to-theme-600',
      bgColor: 'from-secondary-50 to-theme-50'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.strength-card, .company-content, .company-image');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-24 bg-white relative overflow-hidden">
      {/* 简约背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-theme-50/30 to-transparent rounded-full translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-secondary-50/20 to-transparent rounded-full -translate-x-1/2 translate-y-1/2" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 公司介绍部分 */}
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-20 items-start mb-32">
          {/* 左侧内容 */}
          <div
            data-index={0}
            className={`company-content space-y-8 ${visibleElements.includes(0) ? 'animate-slide-in-left' : 'opacity-0'}`}
          >
            {/* 标签 */}
            <div className="inline-flex items-center gap-3 px-4 py-2 rounded-full border border-theme-100">
              <Building2 className="h-4 w-4 text-theme-600" />
              <span className="text-sm font-medium text-theme-700">关于我们</span>
            </div>

            {/* 标题 */}
            <div className="space-y-4">
              <h2 className="text-4xl lg:text-5xl font-bold text-gray-900 leading-tight">
                我们的<span className="text-theme-600">愿景</span>
              </h2>
              <p className="text-xl text-gray-600 font-medium">
                让运筹优化技术赋能千行百业
              </p>
              <div className="w-16 h-1 bg-theme-600 rounded-full" />
            </div>

            {/* 描述文本 */}
            <div className="space-y-6 text-gray-600 leading-relaxed">
              <p>
                <HLink href="https://icode.pku.edu.cn/" target="_blank" className='text-theme-600 hover:text-theme-700 font-medium'>
                  北京大学长沙计算与数字经济研究院
                </HLink>
                是北京大学长沙市人民政府和湖南湘江新区(长沙高新区)管理委员会共建的新型研发机构，
                也是湖南省人民政府和北京大学共建的重大创新平台。
              </p>
              <p>
                研究院以建设高水平新型研发机构为目标，围绕先进计算、大数据、数字经济与数字化转型等领域的核心科学技术问题和重大应用难题，
                广泛集聚全球顶尖人才团队，开展基础研究、应用研究和政策研究，培养创新人才，孵化科技企业，
                构建具有国际影响力、引领国内先进计算和数字经济发展的人才聚集平台、前沿研究平台和成果转化平台。
              </p>
              <p>
                <HLink href="https://icode.pku.edu.cn/techs/tech5/" target="_blank" className='text-theme-600 hover:text-theme-700 font-medium'>
                  工业智能研究中心
                </HLink>
                依托北京大学北京国际数学研究中心长聘教授
                <HLink href="http://faculty.bicmr.pku.edu.cn/~wenzw/" target="_blank" className='text-theme-600 hover:text-theme-700 font-medium'>
                  文再文
                </HLink>
                教授及其团队在运筹优化最优化算法研究中长期积累的前沿成果，
                组建由数学建模专家、优化算法研究员、解决方案工程师及软件工程师等构成的核心研发团队，
                长期致力于运用运筹优化算法与人工智能技术解决企业在生产、仓储、调度、销售等一系列业务场景中的决策优化问题，
                帮助企业降本增效，实现基于数据的智能决策赋能企业数智化转型升级。
              </p>
            </div>

            {/* CTA按钮 */}
            <Link
              to="/about"
              className="group inline-flex items-center gap-2 px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300 shadow-lg hover:shadow-xl"
            >
              <span>了解更多</span>
              <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
            </Link>
          </div>

          {/* 右侧图片 */}
          <div
            data-index={1}
            className={`company-image h-full flex justify-center items-center ${visibleElements.includes(1) ? 'animate-slide-in-right' : 'opacity-0'}`}
          >
            <div className="relative aspect-[4/3] rounded-2xl overflow-hidden shadow-2xl">
              <img
                src="https://images.pexels.com/photos/3184306/pexels-photo-3184306.jpeg?auto=compress&cs=tinysrgb&w=800"
                alt="公司团队"
                className="w-full h-full object-cover"
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/20 via-transparent to-transparent" />
            </div>
          </div>
        </div>

        {/* 核心优势部分 */}
        <div className="text-center py-16">
          <h3 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            我们的<span className="text-theme-600">核心优势</span>
          </h3>
          <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          <p className="text-xl text-gray-600 max-w-2xl mx-auto">
            凭借深厚的技术积淀和丰富的行业经验，为客户提供卓越的服务
          </p>
        </div>

        {/* 优势卡片网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {strengths.map((strength, index) => (
            <div
              key={index}
              data-index={index + 2}
              className={`strength-card group p-8 bg-white rounded-2xl border border-gray-100 hover:border-theme-200 hover:shadow-xl transition-all duration-300 ${
                visibleElements.includes(index + 2) ? 'animate-scale-in' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* 图标 */}
              <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl mb-6 group-hover:bg-theme-100 group-hover:scale-110 transition-all duration-300">
                {strength.icon}
              </div>

              {/* 标题 */}
              <h4 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-theme-700 transition-colors duration-300">
                {strength.title}
              </h4>

              {/* 描述 */}
              <p className="text-gray-600 mb-4 h-16 leading-relaxed text-sm">
                {strength.description}
              </p>

              {/* 统计信息 */}
              <div className="text-sm font-semibold text-theme-600 border-t border-gray-100 pt-4">
                {strength.stats}
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default CompanyStrength;
