import React, { useState, useEffect, useRef } from 'react';
import { <PERSON>, Quote, Sparkles, Users } from 'lucide-react';
import BackgroundDecorations from './BackgroundDecorations';

const ClientsPartners: React.FC = () => {
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const partners = [
    { name: 'Microsoft', logo: 'https://via.placeholder.com/150x60/790606/FFFFFF?text=Microsoft' },
    { name: 'Google', logo: 'https://via.placeholder.com/150x60/C5AA89/FFFFFF?text=Google' },
    { name: 'Amazon', logo: 'https://via.placeholder.com/150x60/790606/FFFFFF?text=Amazon' },
    { name: 'IBM', logo: 'https://via.placeholder.com/150x60/C5AA89/FFFFFF?text=IBM' },
    { name: 'Oracle', logo: 'https://via.placeholder.com/150x60/790606/FFFFFF?text=Oracle' },
    { name: 'SAP', logo: 'https://via.placeholder.com/150x60/C5AA89/FFFFFF?text=SAP' },
    { name: 'Salesforce', logo: 'https://via.placeholder.com/150x60/790606/FFFFFF?text=Salesforce' },
    { name: 'Adobe', logo: 'https://via.placeholder.com/150x60/C5AA89/FFFFFF?text=Adobe' }
  ];

  const testimonials = [
    {
      quote: "OptSuite的解决方案帮助我们显著提升了供应链效率，成本降低了30%，效果超出预期。",
      author: "张总",
      company: "某制造集团",
      position: "供应链总监",
      avatar: "https://images.pexels.com/photos/3184639/pexels-photo-3184639.jpeg?auto=compress&cs=tinysrgb&w=200",
      rating: 5,
      improvement: "成本降低30%"
    },
    {
      quote: "专业的技术团队和优质的服务让我们的数字化转型更加顺利，合作非常愉快。",
      author: "李经理",
      company: "某科技公司",
      position: "技术总监",
      avatar: "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=200",
      rating: 5,
      improvement: "效率提升40%"
    },
    {
      quote: "智能排产系统的实施让我们的生产效率提升了45%，ROI非常可观。",
      author: "王主任",
      company: "某工厂",
      position: "生产经理",
      avatar: "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=200",
      rating: 5,
      improvement: "效率提升45%"
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.testimonial-card, .partners-section');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-24 bg-gradient-to-br from-white via-warm-50 to-theme-50 relative overflow-hidden">
      <BackgroundDecorations particleCount={15} particleOpacity={30} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-3 bg-gradient-to-r from-theme-100 to-secondary-100 rounded-full px-6 py-3 mb-6 shadow-lg">
            <div className="p-2 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full">
              <Users className="h-5 w-5 text-white" />
            </div>
            <span className="text-theme-700 font-medium">客户与合作伙伴</span>
            <Sparkles className="h-5 w-5 text-secondary-600 animate-pulse" />
          </div>
          <h2 className="heading-secondary text-dark mb-6">
            <span className="text-gradient-primary">信任</span>与<span className="text-gradient-secondary">合作</span>
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
          <p className="text-xl max-w-3xl mx-auto leading-relaxed">
            我们与全球知名企业建立了长期合作关系，共同推进智能优化技术的发展
          </p>
        </div>

        {/* Partners Logos */}
        <div 
          data-index={0}
          className={`partners-section relative overflow-hidden mb-20 ${
            visibleElements.includes(0) ? 'animate-fade-in-up' : 'opacity-0'
          }`}
        >
          <div className="bg-white/80 backdrop-blur-sm rounded-3xl p-8 shadow-lg border border-secondary-200/50">
            <div className="flex animate-scroll">
              {[...partners, ...partners].map((partner, index) => (
                <div
                  key={index}
                  className="flex-shrink-0 mx-8 grayscale hover:grayscale-0 transition-all duration-300 hover:scale-110"
                >
                  <img
                    src={partner.logo}
                    alt={partner.name}
                    className="h-16 w-auto opacity-60 hover:opacity-100 transition-opacity duration-300"
                  />
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* Testimonials */}
        <div className="text-center mb-12">
          <h3 className="text-3xl font-bold text-dark mb-4">
            客户<span className="text-gradient-primary">评价</span>
          </h3>
          <p className="text-lg">听听我们的客户怎么说</p>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
          {testimonials.map((testimonial, index) => (
            <div
              key={index}
              data-index={index + 1}
              className={`testimonial-card card-enhanced hover-lift group relative overflow-hidden ${
                visibleElements.includes(index + 1) ? 'animate-scale-in' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Background Gradient */}
              <div className="absolute inset-0 bg-gradient-to-br from-theme-50 to-secondary-50 opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10" />
              
              <div className="relative p-8 z-10">
                {/* Quote Icon */}
                <div className="absolute top-4 right-4 w-12 h-12 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full flex items-center justify-center opacity-20 group-hover:opacity-40 transition-opacity duration-300">
                  <Quote className="h-6 w-6 text-white" />
                </div>

                {/* Rating */}
                <div className="flex items-center mb-4">
                  {[...Array(testimonial.rating)].map((_, i) => (
                    <Star key={i} className="h-5 w-5 text-secondary-500 fill-current" />
                  ))}
                  <span className="ml-2 text-sm text-light">({testimonial.rating}.0)</span>
                </div>

                {/* Quote */}
                <blockquote className="text-medium italic mb-6 leading-relaxed group-hover:text-dark transition-colors duration-300">
                  "{testimonial.quote}"
                </blockquote>

                {/* Improvement Badge */}
                <div className="inline-flex items-center bg-gradient-to-r from-theme-100 to-secondary-100 rounded-full px-4 py-2 mb-6">
                  <span className="text-sm font-semibold text-theme-700">{testimonial.improvement}</span>
                </div>

                {/* Author */}
                <div className="flex items-center">
                  <img
                    src={testimonial.avatar}
                    alt={testimonial.author}
                    className="w-12 h-12 rounded-full object-cover mr-4 ring-2 ring-secondary-200 group-hover:ring-secondary-400 transition-all duration-300"
                  />
                  <div>
                    <h4 className="font-bold text-dark group-hover:text-theme-700 transition-colors duration-300">
                      {testimonial.author}
                    </h4>
                    <p className="text-sm text-light">{testimonial.position}</p>
                    <p className="text-sm font-medium">{testimonial.company}</p>
                  </div>
                </div>
              </div>

              {/* Decorative Corner */}
              <div className="absolute bottom-0 left-0 w-16 h-16 bg-gradient-to-tr from-secondary-500/20 to-transparent rounded-tr-3xl opacity-0 group-hover:opacity-100 transition-opacity duration-300" />
            </div>
          ))}
        </div>

        {/* Stats Section */}
        <div className="mt-20 bg-gradient-to-r from-theme-600 via-theme-700 to-secondary-600 rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl">
          {/* Background Pattern */}
          <div className="absolute inset-0 opacity-10">
            <div className="absolute top-0 left-0 w-32 h-32 border border-white/30 rounded-full animate-float" />
            <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-lg rotate-45 animate-pulse" />
            <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rotate-12" />
          </div>

          <div className="relative z-10 text-center">
            <h3 className="text-3xl md:text-4xl font-bold mb-8">
              值得信赖的合作伙伴
            </h3>
            <div className="grid grid-cols-1 md:grid-cols-4 gap-8">
              {[
                { number: '500+', label: '企业客户' },
                { number: '98%', label: '客户满意度' },
                { number: '15+', label: '年服务经验' },
                { number: '24/7', label: '技术支持' }
              ].map((stat, index) => (
                <div key={index} className="text-center">
                  <div className="text-4xl md:text-5xl font-bold mb-3 text-secondary-200">
                    {stat.number}
                  </div>
                  <div className="text-theme-100 font-medium">{stat.label}</div>
                </div>
              ))}
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ClientsPartners;
