import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Header from './components/Header';
import Footer from './components/Footer';
import Home from './pages/Home';
import Products from './pages/Products';
import ProductDetail from './pages/ProductDetail';
import Solutions from './pages/Solutions';
import SolutionDetail from './pages/SolutionDetail';
import About from './pages/About';
import Documentation from './pages/Documentation';
import ContactForm from './components/ContactForm';
import Cases from './pages/Cases';
import { HeroUIProvider } from '@heroui/react';
import { ThemeProvider } from './context/ThemeContext';
import ThemeTest from '@/components/ThemeTest';

function App() {
  return (
    <Router basename="/official">
      <ThemeProvider>
        <HeroUIProvider className="min-h-screen bg-white">
          <Header />
          <main>
            <Routes>
              <Route path="/" element={<Home />} />
              <Route path="/products" element={<Products />} />
              <Route path="/products/:productId" element={<ProductDetail />} />
              <Route path="/solutions/:solutionId" element={<SolutionDetail />} />
              <Route path="/about" element={<About />} />
              <Route path="/documentation" element={<Documentation />} />
              <Route path="/contact" element={<ContactForm />} />
              <Route path="/cases" element={<Cases />} />
              <Route path="/theme-test" element={<ThemeTest />} />
            </Routes>
          </main>
          <Footer />
        </HeroUIProvider>
      </ThemeProvider>
    </Router>
  );
}

export default App;

