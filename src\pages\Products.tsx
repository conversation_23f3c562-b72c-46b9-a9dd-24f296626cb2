import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Zap, Cpu, Target, ArrowRight, Check, Sparkles, Package } from 'lucide-react';
import BackgroundDecorations from '../components/BackgroundDecorations';

const Products: React.FC = () => {
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const products = [
    {
      id: 'optsuite',
      name: 'OptSuite',
      tagline: '全方位智能优化平台',
      description: '集成多种优化算法的综合平台，支持复杂业务场景的智能决策和自动化处理。',
      icon: <Zap className="h-12 w-12" />,
      features: [
        '多算法集成框架',
        '实时数据处理',
        '可视化分析仪表板',
        '云端部署支持',
        '自定义业务规则',
        'API接口集成'
      ],
      price: '企业版',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100',
      image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800'
    },
    {
      id: 'optaps',
      name: 'OptAPS',
      tagline: '先进生产排程系统',
      description: '基于先进算法的生产排程系统，实现动态排产和资源优化配置。',
      icon: <Cpu className="h-12 w-12" />,
      features: [
        '动态排产算法',
        '资源优化配置',
        '实时监控预警',
        '性能分析报告',
        '多工厂协同',
        '移动端支持'
      ],
      price: '专业版',
      color: 'from-secondary-600 to-secondary-700',
      bgGradient: 'from-secondary-50 to-secondary-100',
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=800'
    },
    {
      id: 'optpinnacle',
      name: 'OptPinnacle',
      tagline: '企业级优化解决方案',
      description: '面向大型企业的端到端优化解决方案，提供全面的业务流程优化。',
      icon: <Target className="h-12 w-12" />,
      features: [
        '端到端流程优化',
        '高级数据分析',
        '智能决策支持',
        '企业级集成',
        '定制化服务',
        '7x24技术支持'
      ],
      price: '旗舰版',
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=800'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.product-card, .comparison-section, .cta-section');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);


  return (
    <div ref={sectionRef}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-40 pb-24 relative overflow-hidden">
        <BackgroundDecorations particleCount={30} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-white/20 shadow-lg">
              <div className="p-2 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full">
                <Package className="h-5 w-5 text-white" />
              </div>
              <span className="text-secondary-200 font-medium">产品中心</span>
              <Sparkles className="h-5 w-5 text-secondary-300 animate-pulse" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-secondary">智能优化</span>产品矩阵
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
            <p className="text-xl text-theme-100 max-w-3xl mx-auto leading-relaxed">
              提供全面的智能优化解决方案，满足不同行业和规模企业的需求
            </p>
          </div>
        </div>
      </section>

      {/* Products Grid */}
      <section className="py-24 bg-gradient-to-br from-warm-50 via-white to-theme-50 relative overflow-hidden">
        {/* Background Decorations */}
        <BackgroundDecorations particleCount={20} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            {products.map((product, index) => (
              <div
                key={product.id}
                data-index={index}
                className={`product-card card-enhanced hover-lift group relative overflow-hidden ${
                  visibleElements.includes(index) ? 'animate-scale-in' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                {/* Background Image */}
                <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Product Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={product.image}
                    alt={product.name}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className={`absolute top-4 left-4 inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r ${product.color} text-white rounded-2xl shadow-lg group-hover:scale-110 group-hover:rotate-3 transition-all duration-300`}>
                    {product.icon}
                  </div>
                  <div className="absolute top-4 right-4">
                    <span className={`bg-gradient-to-r ${product.color} text-white px-4 py-2 rounded-full text-sm font-medium shadow-lg`}>
                      {product.price}
                    </span>
                  </div>
                </div>

                {/* Product Content */}
                <div className="relative p-8 z-10">
                  <h3 className="text-2xl font-bold text-dark mb-3 group-hover:text-theme-700 transition-colors duration-300">
                    {product.name}
                  </h3>
                  
                  <p className="text-theme-600 font-semibold mb-4">{product.tagline}</p>
                  <p className="text-medium mb-6 leading-relaxed">{product.description}</p>

                  {/* Features */}
                  <div className="space-y-3 mb-8">
                    {product.features.map((feature, featureIndex) => (
                      <div
                        key={featureIndex}
                        className="flex items-center group/feature"
                        style={{ animationDelay: `${(index * 0.2) + (featureIndex * 0.1)}s` }}
                      >
                        <Check className="h-5 w-5 text-theme-600 mr-3 group-hover/feature:scale-110 transition-transform duration-200" />
                        <span className="text-medium group-hover/feature:text-dark transition-colors duration-200">
                          {feature}
                        </span>
                      </div>
                    ))}
                  </div>

                  {/* CTA Button */}
                  <Link
                    to={`/products/${product.id}`}
                    className={`w-full inline-flex items-center justify-center px-6 py-4 bg-gradient-to-r ${product.color} text-white font-semibold rounded-xl hover:shadow-lg transition-all duration-300 transform hover:-translate-y-1 relative overflow-hidden group/btn`}
                  >
                    <span className="relative z-10">了解详情</span>
                    <ArrowRight className="ml-3 h-5 w-5 group-hover/btn:translate-x-1 transition-transform relative z-10" />
                    <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover/btn:translate-x-full transition-transform duration-700" />
                  </Link>
                </div>

                {/* Hover Gradient Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${product.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`} />
              </div>
            ))}
          </div>
        </div>
      </section>

      {/* Comparison Section */}
      <section 
        data-index={3}
        className={`comparison-section py-24 bg-white relative overflow-hidden ${
          visibleElements.includes(3) ? 'animate-fade-in-up' : 'opacity-0'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-16">
            <h2 className="text-4xl font-bold text-dark mb-6">
              产品<span className="text-primary">对比</span>
            </h2>
            <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
            <p className="text-xl max-w-2xl mx-auto">
              选择最适合您需求的产品
            </p>
          </div>

          <div className="overflow-x-auto">
            <div className="bg-white rounded-2xl shadow-xl overflow-hidden border border-secondary-200/50">
              <table className="w-full">
                <thead className="bg-gradient-to-r from-theme-50 to-secondary-50">
                  <tr>
                    <th className="px-6 py-4 text-left text-sm font-semibold text-dark">功能特性</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-dark">OptSuite</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-dark">OptAPS</th>
                    <th className="px-6 py-4 text-center text-sm font-semibold text-dark">OptPinnacle</th>
                  </tr>
                </thead>
                <tbody className="divide-y divide-gray-200">
                  {[
                    { feature: '多算法支持', suite: '✓', aps: '✓', pinnacle: '✓' },
                    { feature: '实时处理', suite: '✓', aps: '✓', pinnacle: '✓' },
                    { feature: '生产排程', suite: '基础', aps: '✓', pinnacle: '✓' },
                    { feature: '企业级集成', suite: '✓', aps: '✓', pinnacle: '✓' },
                    { feature: '定制化服务', suite: '✓', aps: '✓', pinnacle: '✓' },
                    { feature: '7x24支持', suite: '-', aps: '✓', pinnacle: '✓' }
                  ].map((row, index) => (
                    <tr key={index} className="hover:bg-theme-50 transition-colors duration-200">
                      <td className="px-6 py-4 text-sm text-dark font-medium">{row.feature}</td>
                      <td className="px-6 py-4 text-center text-sm text-medium">{row.suite}</td>
                      <td className="px-6 py-4 text-center text-sm text-medium">{row.aps}</td>
                      <td className="px-6 py-4 text-center text-sm text-medium">{row.pinnacle}</td>
                    </tr>
                  ))}
                </tbody>
              </table>
            </div>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section 
        data-index={4}
        className={`cta-section py-24 bg-gradient-to-r from-theme-600 via-theme-700 to-secondary-600 relative overflow-hidden ${
          visibleElements.includes(4) ? 'animate-fade-in-up' : 'opacity-0'
        }`}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 border border-white/30 rounded-full animate-float" />
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-lg rotate-45 animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rotate-12" />
          <div className="absolute top-1/4 right-1/3 w-20 h-20 bg-secondary-500/20 rounded-full animate-float" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h2 className="text-4xl md:text-5xl font-bold text-white mb-6">
            准备开始您的优化之旅？
          </h2>
          <p className="text-xl text-theme-100 mb-10 max-w-2xl mx-auto">
            我们的专家团队将为您量身定制最适合的解决方案
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="group inline-flex items-center px-8 py-4 bg-white text-theme-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden"
            >
              <span className="relative z-10">立即咨询</span>
              <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-theme-50/0 via-theme-50/50 to-theme-50/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
            </Link>
            <Link
              to="/documentation"
              className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300 backdrop-blur-sm"
            >
              查看文档
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Products;
