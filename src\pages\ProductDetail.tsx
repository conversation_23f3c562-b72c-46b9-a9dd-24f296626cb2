import React from 'react';
import { usePara<PERSON>, Link } from 'react-router-dom';
import OptSuiteDetail from '../components/products/OptSuiteDetail';
import OptAPSDetail from '../components/products/OptAPSDetail';
import OptPinnacleDetail from '../components/products/OptPinnacleDetail';

const ProductDetail: React.FC = () => {
  const { productId } = useParams<{ productId: string }>();

  // 根据产品ID渲染对应的组件
  const renderProductComponent = () => {
    switch (productId) {
      case 'optsuite':
        return <OptSuiteDetail />;
      case 'optaps':
        return <OptAPSDetail />;
      case 'optpinnacle':
        return <OptPinnacleDetail />;
      default:
        return (
          <div className="pt-16 min-h-screen flex items-center justify-center bg-gray-50">
            <div className="text-center">
              <h1 className="text-4xl font-bold text-gray-900 mb-4">产品未找到</h1>
              <Link to="/products" className="text-theme-600 hover:text-theme-700">
                返回产品列表
              </Link>
            </div>
          </div>
        );
    }
  };

  return renderProductComponent();
};

export default ProductDetail;
