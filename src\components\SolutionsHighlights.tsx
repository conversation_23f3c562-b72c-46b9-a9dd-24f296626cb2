import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, Users, Factory, ArrowRight, Lightbulb, CheckCircle } from 'lucide-react';

const SolutionsHighlights: React.FC = () => {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const solutions = [
    {
      id: 'supply-chain-optimization',
      title: '经营端 - 供应链协同优化',
      description: '通过智能算法优化供应链各环节，降低成本，提升效率，实现全链路数字化管理。',
      icon: <TrendingUp className="h-5 w-5" />,
      type: '仓储配载协同一体化',
      client: '纸制品加工企业',
      improvement: '助力企业交付0阻塞',
      image: 'https://images.pexels.com/photos/3184160/pexels-photo-3184160.jpeg?auto=compress&cs=tinysrgb&w=600',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100',
      accentColor: 'theme-600'
    },
    {
      id: 'smart-scheduling',
      title: '生产端 - 智能排产排班',
      description: '基于实时数据的动态排产系统，提高生产效率和资源利用率，优化生产流程。',
      icon: <Factory className="h-6 w-6" />,
      type: '精密零部件生产排程',
      client: '精密零部件制造商',
      improvement: '助力企业智造“出海”',
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600',
      color: 'from-secondary-600 to-secondary-900',
      bgGradient: 'from-secondary-50 to-secondary-200',
      accentColor: 'secondary-600'
    },
    {
      id: 'workforce-optimization',
      title: '物流端 - 资源调度优化',
      description: '智能匹配运力需求，动态调配仓储库存，优化配送路径规划，降低物流运营成本。提升配送时效与资源利用率，减少运输损耗。',
      icon: <Users className="h-6 w-6" />,
      type: '特殊限制车辆路径规划',
      client: '石化能源企业',
      improvement: '助力企业降本增效',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=600',
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100',
      accentColor: 'theme-700'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleCards(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.2 }
    );

    const cards = sectionRef.current?.querySelectorAll('.solution-card');
    cards?.forEach((card) => observer.observe(card));

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-24 bg-gray-50 relative overflow-hidden">
      {/* 简约背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 right-0 w-96 h-96 bg-gradient-to-bl from-theme-50/40 to-transparent rounded-full translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 left-0 w-96 h-96 bg-gradient-to-tr from-secondary-50/30 to-transparent rounded-full -translate-x-1/2 translate-y-1/2" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 标题区域 */}
        <div className="text-center mb-16">
          {/* 标签 */}
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-white rounded-full border border-gray-200 shadow-sm mb-6">
            <Lightbulb className="h-4 w-4 text-theme-600" />
            <span className="text-sm font-medium text-gray-700">智能解决方案</span>
          </div>

          {/* 主标题 */}
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            我们的智能决策<span className="text-theme-600">解决方案</span>
          </h2>
          <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />

          {/* 副标题 */}
          <p className="text-xl text-gray-600 max-w-2xl mx-auto mb-4">
            量身定制 + 决策建模 + 数据驱动
          </p>

          <p className="text-base text-gray-500 max-w-3xl mx-auto">
            通过前沿人工智能技术与丰富行业经验积累，为您解决最为头疼的复杂决策问题
          </p>
        </div>

        {/* 解决方案卡片网格 */}
        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {solutions.map((solution, index) => (
            <div
              key={solution.id}
              data-index={index}
              className={`solution-card group bg-white rounded-2xl overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 border border-gray-200 hover:border-theme-200 ${
                visibleCards.includes(index) ? 'animate-fade-in-up' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* 图片区域 */}
              <div className="relative h-48 overflow-hidden">
                <img
                  src={solution.image}
                  alt={solution.title}
                  className="w-full h-full object-cover group-hover:scale-105 transition-all duration-500"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />

                {/* 图标 */}
                <div className="absolute top-4 left-4 inline-flex items-center justify-center w-10 h-10 bg-white/95 text-theme-600 rounded-lg shadow-lg">
                  {solution.icon}
                </div>

                {/* 成功案例标签 */}
                <div className="absolute top-4 right-4">
                  <div className="flex items-center gap-1 bg-white/90 text-gray-700 px-3 py-1 rounded-full text-xs font-medium">
                    <CheckCircle className="h-3 w-3 text-green-500" />
                    <span>成功案例</span>
                  </div>
                </div>
              </div>

              {/* 内容区域 */}
              <div className="p-6">
                {/* 标题 */}
                <h3 className="text-lg font-bold text-gray-900 mb-3 group-hover:text-theme-700 transition-colors duration-300">
                  {solution.title}
                </h3>

                {/* 描述 */}
                <p className="text-gray-600 leading-relaxed mb-6 text-sm h-16">
                  {solution.description}
                </p>

                {/* 案例信息 */}
                <div className="space-y-3 mb-6">
                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">解决方案</span>
                    <span className="text-gray-700 font-medium">{solution.type}</span>
                  </div>

                  <div className="flex items-center justify-between text-sm">
                    <span className="text-gray-500">服务客户</span>
                    <span className="text-gray-700 font-medium">{solution.client}</span>
                  </div>

                  <div className="pt-3 border-t border-gray-100">
                    <div className="inline-flex items-center gap-2 px-3 py-1.5 bg-theme-50 rounded-full">
                      <div className="w-1.5 h-1.5 bg-theme-600 rounded-full"></div>
                      <span className="text-theme-700 font-medium text-sm">
                        {solution.improvement}
                      </span>
                    </div>
                  </div>
                </div>

                {/* 链接按钮 */}
                <Link
                  to={`/solutions/${solution.id}`}
                  className="group/link inline-flex items-center justify-center w-full py-2.5 px-4 bg-gray-50 hover:bg-theme-50 text-gray-700 hover:text-theme-700 font-medium rounded-lg transition-all duration-300 border border-gray-200 hover:border-theme-300"
                >
                  <span>了解详情</span>
                  <ArrowRight className="ml-2 h-4 w-4 group-hover/link:translate-x-1 transition-transform duration-300" />
                </Link>
              </div>
            </div>
          ))}
        </div>
      </div>
    </section>
  );
};

export default SolutionsHighlights;
