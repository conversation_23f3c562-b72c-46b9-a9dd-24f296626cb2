import React from "react";
import { <PERSON> } from "react-router-dom";
import ReactPlayer from "react-player";
import {
  ArrowRight,
  Factory,
  Brain,
  Calendar,
  RefreshCw,
  ExternalLink,
  Settings,
  Check,
} from "lucide-react";
import ApsDemo from "@/assets/aps-demo.mp4";
import PrecisionImg from '@/assets/precision.jpg';

const OptAPSDetail: React.FC = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              OptAPS - 您的智能排产专家
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              寻求生产资源效益最大化的最优解
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to={"http://111.22.6.97:18081/aps/#/"}
                target="_blank"
                className="group inline-flex items-center justify-center px-8 py-4 bg-white text-theme-900 font-medium rounded-lg hover:bg-gray-100 transition-all duration-300 shadow-lg"
              >
                <span>立即试用</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* OptAPS介绍 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6 flex flex-col gap-2">
                OptAPS <span>为工业领域设计的下一代高级排产工具</span>
              </h2>
              <div className="w-16 h-1 bg-theme-600 mb-6 rounded-full" />
              <p className="text-lg text-gray-600 leading-relaxed">
                OptAPS以工业领域大模型为基础，整合机器学习、人工智能、运筹学以及复杂问题求解技术，重点解决大规模离散制造场景中小批量、多品种、多批次的复杂任务调度问题，可在
                2
                分钟内针对百级机台、千级订单、万级工序规模下求解高质量排产结果。目前产品已应用于医疗器械、高精密机器人及3C
                零部件制造领域，同时未来可广泛应用于航空航天、芯片半导体等行业。
              </p>
            </div>

            {/* 右侧视频 */}
            <div className="h-full bg-gradient-to-br from-theme-50 to-secondary-50 rounded-2xl p-2 shadow-lg">
              <ReactPlayer
                src={ApsDemo}
                controls
                width="100%"
                height="100%"
              />
            </div>
          </div>
        </div>
      </section>

      {/* 优势 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">产品优势</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          </div>

          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* AI工艺生成 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Brain className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                AI工艺生成
              </h3>
              <p className="text-gray-600 text-center mb-6">快速适配工艺方案</p>

              <div className="space-y-3">
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">自主工艺生成</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">快速调整工艺方案</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">多种实际场合调优</span>
                </div>
              </div>
            </div>

            {/* 中长期排产计划 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <Calendar className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                中长期排产计划
              </h3>
              <p className="text-gray-600 text-center mb-6">指导供应链布局</p>

              <div className="space-y-3">
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">中长期物料采购计划</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">综合评估产能情况</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">上下游供应链协同</span>
                </div>
              </div>
            </div>

            {/* 短期排产计划 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-6">
                <RefreshCw className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-4 text-center">
                短期排产计划
              </h3>
              <p className="text-gray-600 text-center mb-6">
                动态调整生产现场异常
              </p>

              <div className="space-y-3">
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">异常重排 柔性应对</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">秒级计算排产结果</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Check className="h-4 w-4 mr-3 text-green-500 flex-shrink-0" />
                  <span className="text-sm">支持web端轻量调用</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 产品案例 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">产品案例</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示OptAPS在复杂排产场景中的卓越表现
            </p>
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* 案例1：精密仪器零部件 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src={PrecisionImg}
                  alt="精密仪器零部件智能排产"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    <Factory className="h-4 w-4 mr-2" />
                    <span>精密制造</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某精密仪器零部件智能排产项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  零件生产排产优化
                </p>
                <p className="text-gray-600 leading-relaxed mb-6 h-32">
                  OptAPS助力精密仪器零部件企业自主工厂生产制造，研发实时动态排产优化模型与算法，在排产效能上大幅领先于传统人工方法，实现了秒级的排产响应速度，显著提升了设备利用率、订单交付准时率、排产效率以及成本管理，有效地解决了在应对生产过程中的"人、机、料、法、环、测"等方面所遇到的动态决策难题。
                </p>

                <Link
                  to="/cases?case=precision-parts"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>

            {/* 案例2：模具加工 */}
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="relative h-48">
                <img
                  src="https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=800"
                  alt="模具加工智能排产"
                  className="w-full h-full object-cover"
                />
                <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                <div className="absolute bottom-4 left-4 right-4 text-white">
                  <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                    <Settings className="h-4 w-4 mr-2" />
                    <span>模具制造</span>
                  </div>
                </div>
              </div>

              <div className="p-6">
                <h3 className="text-xl font-bold text-gray-900 mb-2">
                  某模具加工智能排产优化项目
                </h3>
                <p className="text-theme-600 font-medium mb-4">
                  模具生产排产优化
                </p>
                <p className="text-gray-600 leading-relaxed mb-6 h-32">
                  OptAPS助力大规模精加工模具生产制造的快速排产，解决复杂的生产线设置和多变的订单需求调度问题、高精度要求和多工序交叉的工作流程瓶颈、优化生产效率和资源分配，实现求解效率提升。
                </p>

                <Link
                  to="/cases?case=mold-processing"
                  className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                >
                  <span>查看详情</span>
                  <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                </Link>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default OptAPSDetail;
