/** @type {import('tailwindcss').Config} */
import { heroui } from "@heroui/react";
import plugin from 'tailwindcss/plugin';

// 自定义主题插件
const themePlugin = plugin(function({ addVariant, addUtilities, theme }) {
  // 添加主题变体
  addVariant('theme-red', '[data-theme="red"] &');
  addVariant('theme-blue', '[data-theme="blue"] &');
  
  // 添加主题相关的工具类
  addUtilities({
    // 主题背景色
    '.bg-primary': {
      'background-color': 'var(--primary-color)',
    },
    '.bg-primary-light': {
      'background-color': 'var(--primary-light)',
    },
    '.bg-primary-dark': {
      'background-color': 'var(--primary-dark)',
    },
    '.bg-accent': {
      'background-color': 'var(--accent-color)',
    },
    '.bg-accent-light': {
      'background-color': 'var(--accent-light)',
    },
    '.bg-accent-dark': {
      'background-color': 'var(--accent-dark)',
    },
    
    // 副主题色背景
    '.bg-secondary': {
      'background-color': 'var(--secondary-color)',
    },
    '.bg-secondary-light': {
      'background-color': 'var(--secondary-light)',
    },
    '.bg-secondary-dark': {
      'background-color': 'var(--secondary-dark)',
    },
    
    // 主题文字颜色
    '.text-primary': {
      'color': 'var(--primary-color)',
    },
    '.text-primary-light': {
      'color': 'var(--primary-light)',
    },
    '.text-primary-dark': {
      'color': 'var(--primary-dark)',
    },
    '.text-accent': {
      'color': 'var(--accent-color)',
    },
    '.text-accent-light': {
      'color': 'var(--accent-light)',
    },
    '.text-accent-dark': {
      'color': 'var(--accent-dark)',
    },
    
    // 副主题色文字
    '.text-secondary': {
      'color': 'var(--secondary-color)',
    },
    '.text-secondary-light': {
      'color': 'var(--secondary-light)',
    },
    '.text-secondary-dark': {
      'color': 'var(--secondary-dark)',
    },
    
    // 主题边框颜色
    '.border-primary': {
      'border-color': 'var(--primary-color)',
    },
    '.border-primary-light': {
      'border-color': 'var(--primary-light)',
    },
    '.border-accent': {
      'border-color': 'var(--accent-color)',
    },
    '.border-secondary': {
      'border-color': 'var(--secondary-color)',
    },
    
    // 主题渐变
    '.bg-gradient-primary': {
      'background': 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-dark) 100%)',
    },
    '.bg-gradient-primary-light': {
      'background': 'linear-gradient(135deg, var(--primary-light) 0%, var(--primary-color) 100%)',
    },
    '.bg-gradient-primary-accent': {
      'background': 'linear-gradient(135deg, var(--primary-color) 0%, var(--accent-color) 100%)',
    },
    '.bg-gradient-accent': {
      'background': 'linear-gradient(135deg, var(--accent-color) 0%, var(--accent-dark) 100%)',
    },
    '.bg-gradient-secondary': {
      'background': 'linear-gradient(135deg, var(--secondary-color) 0%, var(--secondary-dark) 100%)',
    },
    '.bg-gradient-primary-secondary': {
      'background': 'linear-gradient(135deg, var(--primary-color) 0%, var(--secondary-color) 100%)',
    },
    
    // 文字渐变
    '.text-gradient-primary': {
      'background': 'linear-gradient(135deg, var(--primary-color) 0%, var(--primary-light) 100%)',
      '-webkit-background-clip': 'text',
      '-webkit-text-fill-color': 'transparent',
      'background-clip': 'text',
    },
    '.text-gradient-accent': {
      'background': 'linear-gradient(135deg, var(--accent-color) 0%, var(--accent-light) 100%)',
      '-webkit-background-clip': 'text',
      '-webkit-text-fill-color': 'transparent',
      'background-clip': 'text',
    },
    
    // 主题阴影
    '.shadow-primary': {
      'box-shadow': '0 10px 30px rgba(var(--primary-rgb), 0.1)',
    },
    '.shadow-primary-hover': {
      'box-shadow': '0 20px 50px rgba(var(--primary-rgb), 0.15)',
    },
    '.shadow-secondary': {
      'box-shadow': '0 10px 30px rgba(var(--secondary-rgb), 0.1)',
    },
    '.shadow-secondary-hover': {
      'box-shadow': '0 20px 50px rgba(var(--secondary-rgb), 0.15)',
    },
    '.shadow-primary-glow': {
      'box-shadow': '0 0 30px rgba(var(--primary-rgb), 0.4)',
    },
    
    // Hover状态的主题色
    '.hover\\:bg-primary:hover': {
      'background-color': 'var(--primary-color)',
    },
    '.hover\\:text-primary:hover': {
      'color': 'var(--primary-color)',
    },
    '.hover\\:border-primary:hover': {
      'border-color': 'var(--primary-color)',
    },
  });
});

export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
    "./src/**/*.{css,less}",
    "./node_modules/@heroui/theme/dist/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        primary: {
          red: "#790606",
          gold: "#C5AA89",
          blue: "#2563eb",
          gray: "#64748b",
          white: "#FFFFFF",
        },
        // 主题色 - 会根据当前主题自动切换
        theme: {
          50: "var(--theme-50)",
          100: "var(--theme-100)",
          200: "var(--theme-200)",
          300: "var(--theme-300)",
          400: "var(--theme-400)",
          500: "var(--theme-500)",
          600: "var(--theme-600)",
          700: "var(--theme-700)",
          800: "var(--theme-800)",
          900: "var(--theme-900)",
          950: "var(--theme-950)",
        },
        // 副主题色 - 会根据当前主题自动切换
        secondary: {
          50: "var(--secondary-50)",
          100: "var(--secondary-100)",
          200: "var(--secondary-200)",
          300: "var(--secondary-300)",
          400: "var(--secondary-400)",
          500: "var(--secondary-500)",
          600: "var(--secondary-600)",
          700: "var(--secondary-700)",
          800: "var(--secondary-800)",
          900: "var(--secondary-900)",
        },
        red: {
          50: "#FEF2F2",
          100: "#FEE2E2",
          200: "#FECACA",
          300: "#FCA5A5",
          400: "#F87171",
          500: "#EF4444",
          600: "#DC2626",
          700: "#B91C1C",
          800: "#991B1B",
          900: "#790606",
          950: "#5A0404",
        },
        gold: {
          50: "#FEFDF8",
          100: "#FDF9F0",
          200: "#F9F0E1",
          300: "#F3E6D2",
          400: "#EDDCC3",
          500: "#E7D2B4",
          600: "#D4BFA0",
          700: "#C5AA89",
          800: "#A08B6F",
          900: "#7B6B55",
          950: "#564B3B",
        },
        blue: {
          50: "#EFF6FF",
          100: "#DBEAFE",
          200: "#BFDBFE",
          300: "#93C5FD",
          400: "#60A5FA",
          500: "#3B82F6",
          600: "#2563EB",
          700: "#1D4ED8",
          800: "#1E40AF",
          900: "#1E3A8A",
          950: "#172554",
        },
        warm: {
          50: "#FEFEFE",
          100: "#FDFDFD",
          200: "#F9F8F7",
          300: "#F5F3F0",
          400: "#F1EEE9",
          500: "#EDE9E2",
          600: "#E5DFD6",
          700: "#DDD5CA",
          800: "#D1C7BA",
          900: "#C5B9AA",
        },
        text: {
          dark: "#2D1B1B",
          medium: "#4A3333",
          light: "#6B4A4A",
        },
      },
      fontFamily: {
        sans: ["Inter", "system-ui", "sans-serif"],
        heading: ["Montserrat", "system-ui", "sans-serif"],
      },
      animation: {
        float: "float 6s ease-in-out infinite",
        "pulse-glow": "pulse-glow 2s ease-in-out infinite",
        gradient: "gradient-shift 4s ease infinite",
        scroll: "scroll 30s linear infinite",
        "fade-in-up": "fadeInUp 0.8s ease-out forwards",
        "slide-in-left": "slideInLeft 0.8s ease-out forwards",
        "slide-in-right": "slideInRight 0.8s ease-out forwards",
        "scale-in": "scaleIn 0.6s ease-out forwards",
        "rotate-in": "rotateIn 0.8s ease-out forwards",
      },
      backdropBlur: {
        custom: "12px",
      },
      boxShadow: {
        custom: "0 10px 30px rgba(121, 6, 6, 0.1)",
        "custom-hover": "0 20px 50px rgba(121, 6, 6, 0.15)",
        "custom-blue": "0 10px 30px rgba(37, 99, 235, 0.1)",
        "custom-blue-hover": "0 20px 50px rgba(37, 99, 235, 0.15)",
        glow: "0 0 30px rgba(197, 170, 137, 0.4)",
        "blue-glow": "0 0 30px rgba(59, 130, 246, 0.4)",
      },
      spacing: {
        18: "4.5rem",
        88: "22rem",
        128: "32rem",
      },
      borderRadius: {
        "4xl": "2rem",
      },
    },
  },
  plugins: [heroui(), themePlugin],
};




