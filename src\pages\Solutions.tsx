import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { TrendingUp, Factory, Users, Globe, Filter, ArrowRight, Sparkles, Target } from 'lucide-react';
import BackgroundDecorations from '../components/BackgroundDecorations';

const Solutions: React.FC = () => {
  const [activeCategory, setActiveCategory] = useState('all');
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const categories = [
    { id: 'all', name: '全部', icon: <Globe className="h-5 w-5" /> },
    { id: 'supply-chain', name: '供应链', icon: <TrendingUp className="h-5 w-5" /> },
    { id: 'manufacturing', name: '制造业', icon: <Factory className="h-5 w-5" /> },
    { id: 'workforce', name: '人力资源', icon: <Users className="h-5 w-5" /> }
  ];

  const solutions = [
    {
      id: 'supply-chain-optimization',
      title: '供应链协同优化',
      category: 'supply-chain',
      description: '通过智能算法优化供应链各环节，实现成本降低和效率提升',
      client: 'ABC制造集团',
      industry: '制造业',
      improvement: '库存成本降低35%',
      image: 'https://images.pexels.com/photos/3184160/pexels-photo-3184160.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['供应链', '成本优化', '库存管理'],
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    },
    {
      id: 'smart-scheduling',
      title: '智能排产排班',
      category: 'manufacturing',
      description: '基于实时数据的动态排产系统，提高生产效率和资源利用率',
      client: 'XYZ工厂',
      industry: '制造业',
      improvement: '生产效率提升45%',
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['排产系统', '效率提升', '资源优化'],
      color: 'from-secondary-600 to-secondary-700',
      bgGradient: 'from-secondary-50 to-secondary-100'
    },
    {
      id: 'workforce-optimization',
      title: '人力资源优化',
      category: 'workforce',
      description: '智能匹配人力资源配置，优化工作流程，提升团队效率',
      client: 'DEF服务公司',
      industry: '服务业',
      improvement: '人力成本节约30%',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['人力资源', '成本节约', '效率优化'],
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100'
    },
    {
      id: 'inventory-management',
      title: '智能库存管理',
      category: 'supply-chain',
      description: '通过预测分析优化库存水平，降低库存成本',
      client: 'GHI零售连锁',
      industry: '零售业',
      improvement: '库存周转率提升50%',
      image: 'https://images.pexels.com/photos/3184287/pexels-photo-3184287.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['库存管理', '预测分析', '成本控制'],
      color: 'from-secondary-700 to-theme-600',
      bgGradient: 'from-secondary-50 to-theme-50'
    },
    {
      id: 'production-planning',
      title: '生产计划优化',
      category: 'manufacturing',
      description: '综合考虑多种约束条件，制定最优生产计划',
      client: 'JKL制造公司',
      industry: '制造业',
      improvement: '准时交付率达95%',
      image: 'https://images.pexels.com/photos/3184431/pexels-photo-3184431.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['生产计划', '约束优化', '准时交付'],
      color: 'from-theme-600 to-secondary-600',
      bgGradient: 'from-theme-50 to-secondary-50'
    },
    {
      id: 'resource-allocation',
      title: '资源配置优化',
      category: 'workforce',
      description: '基于数据分析的资源配置决策支持系统',
      client: 'MNO咨询公司',
      industry: '咨询服务',
      improvement: '资源利用率提升40%',
      image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=600',
      tags: ['资源配置', '决策支持', '利用率优化'],
      color: 'from-secondary-600 to-theme-700',
      bgGradient: 'from-secondary-50 to-theme-100'
    }
  ];

  const filteredSolutions = activeCategory === 'all' 
    ? solutions 
    : solutions.filter(solution => solution.category === activeCategory);

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.solution-card, .filter-section, .cta-section');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  return (
    <div ref={sectionRef}>
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-40 pb-24 relative overflow-hidden">
        <BackgroundDecorations particleCount={25} />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-white/20 shadow-lg">
              <div className="p-2 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full">
                <Target className="h-5 w-5 text-white" />
              </div>
              <span className="text-secondary-200 font-medium">解决方案</span>
              <Sparkles className="h-5 w-5 text-secondary-300 animate-pulse" />
            </div>
            <h1 className="text-5xl md:text-6xl font-bold mb-6">
              <span className="text-secondary">智能优化</span>解决方案
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
            <p className="text-xl text-theme-100 max-w-3xl mx-auto mb-8 leading-relaxed">
              通过丰富的行业经验和成功案例，为您提供最优的业务解决方案
            </p>
            <div className="flex items-center justify-center">
              <Filter className="h-5 w-5 mr-2" />
              <span className="text-secondary-200">按行业分类浏览</span>
            </div>
          </div>
        </div>
      </section>

      {/* Category Filter */}
      <section 
        data-index={0}
        className={`filter-section bg-white py-8 shadow-sm relative overflow-hidden ${
          visibleElements.includes(0) ? 'animate-fade-in-up' : 'opacity-0'
        }`}
      >
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex flex-wrap justify-center gap-4">
            {categories.map((category) => (
              <button
                key={category.id}
                onClick={() => setActiveCategory(category.id)}
                className={`inline-flex items-center px-6 py-3 rounded-full font-medium transition-all duration-200 transform hover:-translate-y-1 ${
                  activeCategory === category.id
                    ? 'bg-gradient-to-r from-theme-600 to-theme-700 text-white shadow-lg'
                    : 'bg-gradient-to-r from-theme-50 to-secondary-50 text-dark hover:from-theme-100 hover:to-secondary-100 border border-theme-200/50'
                }`}
              >
                {category.icon}
                <span className="ml-2">{category.name}</span>
              </button>
            ))}
          </div>
        </div>
      </section>

      {/* Solutions Grid */}
      <section className="py-24 bg-gradient-to-br from-warm-50 via-white to-theme-50 relative overflow-hidden">
        {/* Background Decorations */}
        <BackgroundDecorations particleCount={20} />

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {filteredSolutions.map((solution, index) => (
              <div
                key={solution.id}
                data-index={index + 1}
                className={`solution-card card-enhanced hover-lift group relative overflow-hidden ${
                  visibleElements.includes(index + 1) ? 'animate-scale-in' : 'opacity-0'
                }`}
                style={{ animationDelay: `${index * 0.2}s` }}
              >
                {/* Background Image */}
                <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                  <img
                    src={solution.image}
                    alt={solution.title}
                    className="w-full h-full object-cover"
                  />
                </div>

                {/* Image */}
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={solution.image}
                    alt={solution.title}
                    className="w-full h-full object-cover group-hover:scale-105 transition-transform duration-500"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/50 to-transparent" />
                  <div className="absolute top-4 right-4">
                    <span className="bg-white/20 backdrop-blur-sm text-white px-3 py-1 rounded-full text-sm font-medium border border-white/30">
                      {solution.industry}
                    </span>
                  </div>
                  <div className="absolute bottom-4 left-4">
                    <span className={`bg-gradient-to-r ${solution.color} text-white px-3 py-1 rounded-full text-sm font-medium shadow-lg`}>
                      成功案例
                    </span>
                  </div>
                </div>

                {/* Content */}
                <div className="relative p-8 z-10">
                  <h3 className="text-xl font-bold text-dark mb-3 group-hover:text-theme-700 transition-colors duration-300">
                    {solution.title}
                  </h3>
                  <p className="text-medium mb-4 leading-relaxed">{solution.description}</p>
                  
                  {/* Tags */}
                  <div className="flex flex-wrap gap-2 mb-6">
                    {solution.tags.map((tag, tagIndex) => (
                      <span
                        key={tagIndex}
                        className="bg-gradient-to-r from-theme-100 to-secondary-100 text-theme-700 px-3 py-1 rounded-full text-sm font-medium border border-theme-200/50"
                      >
                        {tag}
                      </span>
                    ))}
                  </div>

                  {/* Case Study */}
                  <div className={`bg-gradient-to-r ${solution.bgGradient} rounded-xl p-6 mb-6 border border-secondary-200/50 shadow-sm`}>
                    <div className="flex items-center justify-between mb-3">
                      <span className="text-sm text-light font-medium">客户案例</span>
                      <span className="text-sm font-bold text-dark">{solution.client}</span>
                    </div>
                    <div className="flex items-center">
                      <div className="flex-1 bg-white/50 rounded-full h-3 overflow-hidden">
                        <div className={`h-3 rounded-full bg-gradient-to-r ${solution.color} w-4/5 animate-pulse-glow`}></div>
                      </div>
                      <span className="ml-4 text-sm font-bold text-theme-700">{solution.improvement}</span>
                    </div>
                  </div>

                  <Link
                    to={`/solutions/${solution.id}`}
                    className="group/link inline-flex items-center text-theme-600 hover:text-theme-700 font-semibold transition-all duration-300"
                  >
                    查看详情
                    <ArrowRight className="ml-2 h-5 w-5 group-hover/link:translate-x-2 transition-transform duration-300" />
                  </Link>
                </div>

                {/* Hover Gradient Overlay */}
                <div className={`absolute inset-0 bg-gradient-to-br ${solution.bgGradient} opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`} />
              </div>
            ))}
          </div>

          {/* Empty State */}
          {filteredSolutions.length === 0 && (
            <div className="text-center py-20">
              <div className="text-theme-400 mb-4">
                <Filter className="h-16 w-16 mx-auto" />
              </div>
              <h3 className="text-2xl font-bold text-dark mb-4">暂无相关解决方案</h3>
              <p className="text-medium mb-8">请尝试切换其他分类或联系我们了解更多信息</p>
              <Link
                to="/contact"
                className="inline-flex items-center px-8 py-3 bg-gradient-to-r from-theme-600 to-theme-700 text-white font-medium rounded-lg hover:from-theme-700 hover:to-theme-800 transition-all duration-200"
              >
                联系我们
                <ArrowRight className="ml-2 h-5 w-5" />
              </Link>
            </div>
          )}
        </div>
      </section>

      {/* CTA Section */}
      <section 
        data-index={7}
        className={`cta-section bg-gradient-to-r from-theme-600 via-theme-700 to-secondary-600 py-24 relative overflow-hidden ${
          visibleElements.includes(7) ? 'animate-fade-in-up' : 'opacity-0'
        }`}
      >
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 border border-white/30 rounded-full animate-float" />
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-lg rotate-45 animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rotate-12" />
          <div className="absolute top-1/4 right-1/3 w-20 h-20 bg-secondary-500/20 rounded-full animate-float" style={{ animationDelay: '2s' }} />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h2 className="text-3xl md:text-4xl font-bold text-white mb-4">
            需要定制化解决方案？
          </h2>
          <p className="text-xl text-theme-100 mb-8">
            我们的专家团队将为您量身定制最适合的解决方案
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="group inline-flex items-center px-8 py-3 bg-white text-theme-600 font-medium rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden"
            >
              <span className="relative z-10">立即咨询</span>
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-theme-50/0 via-theme-50/50 to-theme-50/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
            </Link>
            <Link
              to="/products"
              className="inline-flex items-center px-8 py-3 border border-white/30 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
            >
              查看产品
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default Solutions;
