import OptAPS from '@/assets/OptAPS.png';
import OptPinnacle from '@/assets/OptPinnacle.png';
import OptSuite from '@/assets/OptSuite.png';
import { 
  AlertTriangle, 
  ArrowRight, 
  Calendar, 
  ChevronLeft, 
  ChevronRight, 
  Clock, 
  Cpu, 
  Factory, 
  Package, 
  Plane, 
  Play, 
  Route, 
  Server, 
  Train, 
  Users 
} from 'lucide-react';
import React, { useEffect, useRef, useState } from 'react';
import { Link } from 'react-router-dom';
import BackgroundDecorations from './BackgroundDecorations';
import ReactPlayer from 'react-player'
import pinnacleDemo from '@/assets/pinnacle-demo.mp4'
import apsDemo from '@/assets/aps-demo.mp4'
import { Modal, ModalContent, ModalHeader, ModalBody, useDisclosure } from "@heroui/react";

const HeroSection: React.FC = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const [isVisible, setIsVisible] = useState(false);
  const heroRef = useRef<HTMLDivElement>(null);
  const { isOpen, onOpen, onOpenChange } = useDisclosure();
  const [openSlide, setOpenSlide] = useState<any>(null);

  const slides = [
    {
      title: 'OptSuite',
      subtitle: '具有自主知识产权的高性能、高扩展的数学规划求解器',
      description: '解决国家战略及产业发展中的大规模复杂优化决策问题，是智能决策场景的"计算芯片"；同时，基于独立研发的核心计算库，为算法开发者提供高效便捷的算法开发框架，构建新一代优化算法科学计算平台。',
      image: OptSuite,
      cta: '了解更多',
      route: '/products/optsuite',
      applications: [
        { name: '航空航天', icon: <Plane className="h-5 w-5" /> },
        { name: '芯片设计', icon: <Cpu className="h-5 w-5" /> },
        { name: '智能制造', icon: <Factory className="h-5 w-5" /> },
        { name: '轨道交通', icon: <Train className="h-5 w-5" /> },
        { name: 'AI超算', icon: <Server className="h-5 w-5" /> }
      ]
    },
    {
      title: 'OptPinnacle',
      subtitle: '从自然语言到科学决策的一站式建模服务平台',
      description: '基于运筹大模型底座的智能决策建模+国产自主可控数学规划求解器为核心支撑的新一代智能决策建模平台',
      image: OptPinnacle,
      cta: '产品试用',
      route: '/products/optpinnacle',
      applications: [
        { name: '路径优化问题', icon: <Route className="h-5 w-5" /> },
        { name: '资源调度问题', icon: <Users className="h-5 w-5" /> },
        { name: '排产排版问题', icon: <Calendar className="h-5 w-5" /> }
      ],
      demo: <ReactPlayer src={pinnacleDemo} controls width={'100%'} height={'100%'}/>
    },
    {
      title: 'OptAPS',
      subtitle: '智能排产优化平台',
      description: '以工业领域大模型为基础，整合机器学习、人工智能、运筹学以及复杂问题求解技术，重点解决大规模离散制造场景中小批量、多品种、多批次的复杂任务调度问题，可在 2 分钟内针对百级机台、千级订单、万级工序规模下求解高质量排产结果。目前产品已应用于医疗器械、高精密机器人及3C 零部件制造领域，同时未来可广泛应用于航空航天、芯片半导体等行业。',
      image: OptAPS,
      cta: '了解更多',
      route: '/products/optaps',
      applications: [
        { name: '动态排程', icon: <Clock className="h-5 w-5" /> },
        { name: '交期答复', icon: <Calendar className="h-5 w-5" /> },
        { name: '物料计划', icon: <Package className="h-5 w-5" /> },
        { name: '紧急插单', icon: <AlertTriangle className="h-5 w-5" /> }
      ],
      demo: <ReactPlayer src={apsDemo} controls width={'100%'} height={'100%'}/>
    },
  ];

  useEffect(() => {
    const timer = setInterval(() => {
      setCurrentSlide((prev) => (prev + 1) % slides.length);
    }, 7000);
    return () => clearInterval(timer);
  }, [slides.length]);

  useEffect(() => {
    setIsVisible(true);
  }, []);

  const nextSlide = () => {
    setCurrentSlide((prev) => (prev + 1) % slides.length);
  };

  const prevSlide = () => {
    setCurrentSlide((prev) => (prev - 1 + slides.length) % slides.length);
  };

  const goToSlide = (index: number) => {
    setCurrentSlide(index);
  };

  const handleDemoOpen = (slide: any) => {
    setOpenSlide(slide);
    onOpen();
  };

  return (
    <section 
      ref={heroRef} 
      className="relative min-h-screen flex items-center justify-center overflow-hidden bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900"
    >

      {/* 主要内容 */}
      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 w-full">
        <div className="grid grid-cols-1 lg:grid-cols-12 gap-16 items-center">
          
          {/* 左侧内容 - 8列 */}
          <div 
            className={`lg:col-span-8 text-white space-y-8 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`}
          >
            
            {/* 标题区域 */}
            <div className="space-y-6">
              <div className="space-y-3">
                <h1 
                  className="text-5xl lg:text-6xl font-bold leading-tight"
                >
                  <span className="block text-white">{slides[currentSlide].title}</span>
                </h1>
                <h2 
                  className="text-xl lg:text-2xl text-secondary-200 font-light leading-relaxed max-w-3xl"
                >
                  {slides[currentSlide].subtitle}
                </h2>
              </div>
              
              <div className="w-16 h-1 bg-gradient-to-r from-secondary-400 to-theme-600 rounded-full"></div>
            </div>

            {/* 描述文字 */}
            <p 
              className="text-lg text-gray-200 leading-relaxed max-w-2xl opacity-90"
            >
              {slides[currentSlide].description}
            </p>

            {/* 应用场景 */}
            <div className="space-y-4">
              <h3 
                className="text-lg font-semibold text-secondary-200"
              >
                应用范围
              </h3>
              <div 
                className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-3"
              >
                {slides[currentSlide].applications.map((app, index) => (
                  <div 
                    key={index}
                    className="group flex flex-col items-center text-center p-4 bg-white/5 backdrop-blur-sm rounded-lg border border-white/10 hover:bg-white/10 hover:border-white/20 transition-all duration-300"
                  >
                    <div 
                      className="text-secondary-300 mb-2 group-hover:text-white transition-colors duration-300"
                    >
                      {app.icon}
                    </div>
                    <span 
                      className="text-sm text-gray-300 group-hover:text-white transition-colors duration-300"
                    >
                      {app.name}
                    </span>
                  </div>
                ))}
              </div>
            </div>

            {/* 操作按钮 */}
            <div 
              className="flex flex-col sm:flex-row gap-4 pt-4"
            >
              {slides[currentSlide].cta && (
                <Link
                  to={slides[currentSlide].route}
                  className="group inline-flex items-center justify-center px-8 py-4 bg-gradient-to-r from-theme-600 to-theme-700 text-white font-semibold rounded-lg hover:from-theme-700 hover:to-theme-800 transition-all duration-300 shadow-lg hover:shadow-xl"
                >
                  <span>{slides[currentSlide].cta}</span>
                  <ArrowRight 
                    className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform duration-300"
                  />
                </Link>
              )}
              
              {slides[currentSlide].demo && (
                <button 
                  onClick={() => handleDemoOpen(slides[currentSlide])}
                  className="group inline-flex items-center justify-center px-8 py-4 bg-white/10 backdrop-blur-sm border border-white/30 text-white font-semibold rounded-lg hover:bg-white/20 hover:border-white/40 transition-all duration-300"
                >
                  <Play className="mr-2 h-5 w-5" />
                  <span>观看演示</span>
                </button>
              )}
            </div>
          </div>

          {/* 右侧图片 - 4列 */}
          <div 
            className={`lg:col-span-4 ${isVisible ? 'animate-fade-in-up' : 'opacity-0'}`} 
            style={{ animationDelay: '0.2s' }}
          >
            <div className="relative">
              
              {/* 主图片容器 */}
              <div 
                className="relative h-96 lg:h-[500px] rounded-2xl overflow-hidden shadow-2xl bg-white/5 backdrop-blur-sm border border-white/10"
              >
                <img
                  src={slides[currentSlide].image}
                  alt={slides[currentSlide].title}
                  className="w-full h-full object-cover transition-all duration-700"
                />
                <div 
                  className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent"
                ></div>
                
                {/* 图片上的标题 */}
                <div 
                  className="absolute bottom-6 left-6 right-6"
                >
                  <h3 
                    className="text-2xl font-bold text-white mb-2"
                  >
                    {slides[currentSlide].title}
                  </h3>
                  <div 
                    className="w-12 h-1 bg-secondary-400 rounded-full"
                  ></div>
                </div>
              </div>

              {/* 导航控制 */}
              <button
                onClick={prevSlide}
                className="absolute left-4 top-1/2 -translate-y-1/2 p-3 bg-black/30 backdrop-blur-sm rounded-full text-white hover:bg-black/50 transition-all duration-300 border border-white/20"
              >
                <ChevronLeft className="h-5 w-5" />
              </button>
              <button
                onClick={nextSlide}
                className="absolute right-4 top-1/2 -translate-y-1/2 p-3 bg-black/30 backdrop-blur-sm rounded-full text-white hover:bg-black/50 transition-all duration-300 border border-white/20"
              >
                <ChevronRight className="h-5 w-5" />
              </button>

              {/* 指示器 */}
              <div 
                className="absolute -bottom-12 left-1/2 -translate-x-1/2 flex space-x-3"
              >
                {slides.map((_, index) => (
                  <button
                    key={index}
                    onClick={() => goToSlide(index)}
                    className={`w-12 h-1 rounded-full transition-all duration-300 ${
                      index === currentSlide 
                        ? 'bg-secondary-400' 
                        : 'bg-white/30 hover:bg-white/50'
                    }`}
                  />
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* 滚动提示 */}
      <div 
        className="absolute bottom-8 left-1/2 -translate-x-1/2 text-center"
      >
        <div 
          className="flex flex-col items-center space-y-2 opacity-70"
        >
          <span 
            className="text-white text-sm"
          >
            向下探索
          </span>
          <div 
            className="w-6 h-10 border-2 border-white/50 rounded-full flex justify-center"
          >
            <div 
              className="w-1 h-3 bg-white/70 rounded-full mt-2 animate-bounce"
            ></div>
          </div>
        </div>
      </div>

      {/* 视频模态框 */}
      <Modal 
        isOpen={isOpen} 
        onOpenChange={onOpenChange}
        size="5xl"
        classNames={{
          backdrop: "bg-black/80 backdrop-blur-sm",
          base: "border-none bg-transparent shadow-none",
          body: "p-0",
          header: "bg-theme-900/95 backdrop-blur-md border-b border-white/10"
        }}
      >
        <ModalContent>
          {(onClose) => (
            <>
              <ModalHeader 
                className="flex flex-col gap-1 text-white"
              >
                <h3 
                  className="text-xl font-semibold"
                >
                  {openSlide?.title} - 产品演示
                </h3>
              </ModalHeader>
              <ModalBody 
                className="bg-black rounded-b-lg overflow-hidden"
              >
                {openSlide?.demo}
              </ModalBody>
            </>
          )}
        </ModalContent>
      </Modal>
    </section>
  );
};

export default HeroSection;
