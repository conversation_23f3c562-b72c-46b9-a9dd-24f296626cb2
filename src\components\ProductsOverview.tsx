import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { Zap, Cpu, Target, ArrowRight, BarChart3, Settings, Globe, CheckCircle } from 'lucide-react';
import BackgroundDecorations from './BackgroundDecorations';

const ProductsOverview: React.FC = () => {
  const [visibleCards, setVisibleCards] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const products = [
    {
      id: 'optsuite',
      name: 'OptSuite',
      description: '全方位智能优化平台，集成多种算法，支持复杂业务场景的智能决策。',
      icon: <Zap className="h-8 w-8" />,
      features: ['多算法集成', '智能决策', '可视化分析', '云端部署'],
      color: 'from-theme-600 to-theme-700',
      hoverColor: 'hover:from-theme-700 hover:to-theme-800',
      bgPattern: 'bg-gradient-to-br from-theme-50 to-theme-100',
      image: 'https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'optaps',
      name: 'OptAPS',
      description: '先进生产排程系统，实现动态排产和资源优化配置。',
      icon: <Cpu className="h-8 w-8" />,
      features: ['动态排产', '资源优化', '实时监控', '性能分析'],
      color: 'from-secondary-600 to-secondary-700',
      hoverColor: 'hover:from-secondary-700 hover:to-secondary-800',
      bgPattern: 'bg-gradient-to-br from-secondary-50 to-secondary-100',
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=600'
    },
    {
      id: 'optpinnacle',
      name: 'OptPinnacle',
      description: '企业级优化解决方案，提供端到端的业务流程优化。',
      icon: <Target className="h-8 w-8" />,
      features: ['流程优化', '数据分析', '决策支持', '集成接口'],
      color: 'from-theme-800 to-secondary-600',
      hoverColor: 'hover:from-theme-900 hover:to-secondary-700',
      bgPattern: 'bg-gradient-to-br from-warm-50 to-warm-100',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=600'
    }
  ];

  const stats = [
    { icon: <BarChart3 className="h-6 w-6" />, value: '99.9%', label: '系统稳定性', color: 'text-theme-600' },
    { icon: <Settings className="h-6 w-6" />, value: '50+', label: '优化算法', color: 'text-secondary-600' },
    { icon: <Globe className="h-6 w-6" />, value: '100+', label: '企业客户', color: 'text-theme-700' }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleCards(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const cards = sectionRef.current?.querySelectorAll('.product-card');
    cards?.forEach((card) => observer.observe(card));

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-24 bg-gradient-to-br from-gray-50 to-white relative overflow-hidden">
      <BackgroundDecorations particleCount={12} particleOpacity={25} />
      
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative">
        {/* Header */}
        <div className="text-center mb-20">
          <div className="inline-flex items-center space-x-2 bg-gradient-to-r from-theme-100 to-secondary-100 rounded-full px-6 py-2 mb-6">
            <Settings className="h-5 w-5 text-theme-600" />
            <span className="text-theme-700 font-medium">产品中心</span>
          </div>
          <h2 className="heading-secondary text-dark mb-6">
            <span className="text-gradient-primary">智能优化</span>产品矩阵
          </h2>
          <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
          <p className="text-xl max-w-3xl mx-auto leading-relaxed">
            提供全面的智能优化解决方案，满足不同行业和规模企业的需求
          </p>
        </div>

        {/* Stats */}
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-20">
          {stats.map((stat, index) => (
            <div
              key={index}
              className="text-center group hover-lift"
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              <div className={`inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-theme-600 to-secondary-500 text-white rounded-2xl mb-6 group-hover:scale-110 transition-transform duration-300 shadow-lg`}>
                {stat.icon}
              </div>
              <div className={`text-4xl font-bold mb-3 ${stat.color}`}>
                {stat.value}
              </div>
              <div className="text-medium font-medium">{stat.label}</div>
            </div>
          ))}
        </div>

        {/* Products Grid */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8 mb-20">
          {products.map((product, index) => (
            <div
              key={product.id}
              data-index={index}
              className={`product-card card-enhanced hover-lift group relative overflow-hidden ${
                visibleCards.includes(index) ? 'animate-fade-in-up' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 0.2}s` }}
            >
              {/* Background Image */}
              <div className="absolute inset-0 opacity-5 group-hover:opacity-10 transition-opacity duration-300">
                <img
                  src={product.image}
                  alt={product.name}
                  className="w-full h-full object-cover"
                />
              </div>

              {/* Content */}
              <div className="relative p-8 z-10">
                {/* Icon */}
                <div className={`inline-flex items-center justify-center w-20 h-20 bg-gradient-to-r ${product.color} text-white rounded-2xl mb-6 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg`}>
                  {product.icon}
                </div>

                {/* Title */}
                <h3 className="text-2xl font-bold text-dark mb-4 group-hover:text-theme-700 transition-colors duration-300">
                  {product.name}
                </h3>

                {/* Description */}
                <p className="text-medium mb-6 leading-relaxed">
                  {product.description}
                </p>
                
                {/* Features */}
                <div className="space-y-3 mb-8">
                  {product.features.map((feature, featureIndex) => (
                    <div
                      key={featureIndex}
                      className="flex items-center group/feature"
                      style={{ animationDelay: `${(index * 0.2) + (featureIndex * 0.1)}s` }}
                    >
                      <CheckCircle className="w-5 h-5 text-theme-600 mr-3 group-hover/feature:scale-110 transition-transform duration-200" />
                      <span className="text-medium group-hover/feature:text-dark transition-colors duration-200">
                        {feature}
                      </span>
                    </div>
                  ))}
                </div>

                {/* CTA Link */}
                <Link
                  to={`/products/${product.id}`}
                  className="group/link inline-flex items-center text-theme-600 hover:text-theme-700 font-semibold transition-all duration-300"
                >
                  了解更多
                  <ArrowRight className="ml-2 h-5 w-5 group-hover/link:translate-x-2 transition-transform duration-300" />
                </Link>
              </div>

              {/* Hover Gradient Overlay */}
              <div className={`absolute inset-0 bg-gradient-to-br ${product.bgPattern} opacity-0 group-hover:opacity-100 transition-opacity duration-300 -z-10`} />
            </div>
          ))}
        </div>

        {/* CTA Section */}
        <div className="text-center">
          <div className="bg-gradient-to-r from-theme-600 to-secondary-600 rounded-3xl p-12 text-white relative overflow-hidden shadow-2xl">
            {/* Background Pattern */}
            <div className="absolute inset-0 opacity-10">
              <div className="absolute top-0 left-0 w-32 h-32 border border-white/30 rounded-full animate-float" />
              <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-lg rotate-45 animate-pulse" />
              <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rotate-12" />
            </div>

            <div className="relative z-10">
              <h3 className="text-3xl md:text-4xl font-bold mb-6">
                准备开始您的优化之旅？
              </h3>
              <p className="text-xl mb-10 text-theme-100 max-w-2xl mx-auto">
                我们的专家团队将为您量身定制最适合的解决方案
              </p>
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Link
                  to="/contact"
                  className="group inline-flex items-center px-8 py-4 bg-white text-theme-600 font-semibold rounded-xl hover:bg-gray-50 transition-all duration-300 shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden"
                >
                  <span className="relative z-10">立即咨询</span>
                  <ArrowRight className="ml-3 h-5 w-5 group-hover:translate-x-1 transition-transform relative z-10" />
                  <div className="absolute inset-0 bg-gradient-to-r from-theme-50/0 via-theme-50/50 to-theme-50/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                </Link>
                <Link
                  to="/documentation"
                  className="inline-flex items-center px-8 py-4 border-2 border-white/30 text-white font-semibold rounded-xl hover:bg-white/10 transition-all duration-300"
                >
                  查看文档
                </Link>
              </div>
            </div>
          </div>
        </div>
      </div>
    </section>
  );
};

export default ProductsOverview;

