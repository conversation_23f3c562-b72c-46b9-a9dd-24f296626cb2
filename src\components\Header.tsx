import logoBlue from "@/assets/logo-blue.svg"; // 需要添加蓝色版本的logo
import logoRed from "@/assets/logo-red.svg";
import logo from "@/assets/logo.svg";
import { ThemeContext } from "@/context/ThemeContext";
import {
  Button,
  Dropdown,
  DropdownItem,
  DropdownMenu,
  DropdownTrigger
} from "@heroui/react";
import { ChevronDown, Menu, Moon, Sun, X } from "lucide-react";
import React, { useContext, useEffect, useState } from "react";
import { Link, useLocation, useNavigate } from "react-router-dom";

const Header: React.FC = () => {
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMobileMenuOpen, setIsMobileMenuOpen] = useState(false);
  const [dropdownOpen, setDropdownOpen] = useState<string | null>(null);
  const location = useLocation();
  const navigate = useNavigate();
  const { theme, toggleTheme } = useContext(ThemeContext);
  
  useEffect(() => {
    const handleScroll = () => {
      setIsScrolled(window.scrollY > 50);
    };
    window.addEventListener("scroll", handleScroll);
    return () => window.removeEventListener("scroll", handleScroll);
  }, []);

  const navigation = [
    { name: "首页", href: "/" },
    {
      name: "产品",
      dropdown: [
        { name: "OptSuite", href: "/products/optsuite" },
        { name: "OptPinnacle", href: "/products/optpinnacle" },
        { name: "OptAPS", href: "/products/optaps" },
      ],
    },
    {
      name: "解决方案",
      dropdown: [
        { name: "经营端 - 供应链协同优化", href: "/solutions/supply-chain-optimization" },
        { name: "生产端 - 智能排产排程", href: "/solutions/smart-scheduling" },
        { name: "物流端 - 资源调度优化", href: "/solutions/workforce-optimization" },
        { name: "其它行业案例", href: "/solutions/others" },
      ],
    },
    {
      name: "文档",
      href: "/documentation",
    },
    {
      name: "关于我们",
      href: "/about"
    },
  ];

  // 根据当前主题获取正确的logo
  const getLogo = () => {
    if (theme === 'blue') {
      return isScrolled ? logoBlue : logo;
    } else {
      return isScrolled ? logoRed : logo;
    }
  };

  // 获取主题相关的样式类
  const getThemeClasses = (type: string, defaultClass: string, redClass: string, blueClass: string) => {
    if (theme === 'blue') {
      return blueClass;
    } else {
      return redClass;
    }
  };

  return (
    <header
      className={`fixed top-0 left-0 right-0 z-50 transition-all duration-200 ${
        isScrolled
          ? "bg-white/95 backdrop-blur-custom shadow-custom"
          : "bg-transparent"
      }`}
    >
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex justify-between items-center h-20">
          {/* Logo */}
          <Link to="/" className="flex items-center space-x-3 group">
            <img
              src={getLogo()}
              alt="OptSuite"
              className="h-12"
            />
          </Link>

          {/* Desktop Navigation */}
          <nav className="hidden md:flex md:items-center space-x-2">
            {navigation.map((item) => (
              <div key={item.name} className="relative">
                {item.dropdown ? (
                  <Dropdown key={item.name} placement="bottom-start" onOpenChange={(open) => {
                    if (open) {
                      setDropdownOpen(item.name);
                      navigate(item.href);
                    } else {
                      setDropdownOpen(null);
                    }
                  }}>
                    <DropdownTrigger>
                      <Button
                        variant="light"
                        className={`${
                          location.pathname.startsWith(item.href)
                            ? isScrolled
                              ? getThemeClasses(
                                  'active-scrolled',
                                  '',
                                  'text-theme-700 bg-theme-50 shadow-sm',
                                  'text-blue-700 bg-blue-50 shadow-sm'
                                )
                              : getThemeClasses(
                                  'active-transparent',
                                  '',
                                  'text-secondary-200 bg-white/10 backdrop-blur-sm',
                                  'text-blue-200 bg-white/10 backdrop-blur-sm'
                                )
                            : isScrolled
                            ? "text-medium "
                            : "text-white/90"
                        }`}
                      >
                        <span
                          className={`flex items-center gap-2 text-sm font-medium transition-all duration-300`}
                        >
                          {item.name}
                          <ChevronDown
                            className={`h-4 w-4 transition-transform duration-200 ${
                              dropdownOpen === item.name ? "rotate-180" : ""
                            }`}
                          />
                        </span>
                      </Button>
                    </DropdownTrigger>
                    <DropdownMenu
                      aria-label={item.name}
                      itemClasses={{
                        base: getThemeClasses(
                          'dropdown-item',
                          '',
                          'hover:!bg-theme-50 hover:!text-theme-700 transition-colors duration-200',
                          'hover:!bg-blue-50 hover:!text-blue-700 transition-colors duration-200'
                        ),
                      }}
                    >
                      {item.dropdown.map((subItem) => (
                        <DropdownItem
                          key={subItem.name}
                          onPress={() => {
                            navigate(subItem.href);
                          }}
                        >
                          {subItem.name}
                        </DropdownItem>
                      ))}
                    </DropdownMenu>
                  </Dropdown>
                ) : (
                  <Link
                    key={item.name}
                    to={item.href}
                    className={`px-4 py-3 rounded-lg text-sm font-medium transition-all duration-300 ${
                      location.pathname === item.href
                        ? isScrolled
                          ? getThemeClasses(
                              'active-scrolled',
                              '',
                              'text-theme-700 bg-theme-50 shadow-sm',
                              'text-theme-700 bg-theme-50 shadow-sm'
                            )
                          : getThemeClasses(
                              'active-transparent',
                              '',
                              'text-secondary-200 bg-white/10 backdrop-blur-sm',
                              'text-theme-200 bg-white/10 backdrop-blur-sm'
                            )
                        : isScrolled
                        ? getThemeClasses(
                            'inactive-scrolled',
                            '',
                            'hover:text-theme-700 hover:bg-theme-50',
                            'hover:text-theme-700 hover:bg-theme-50'
                          )
                        : getThemeClasses(
                            'inactive-transparent',
                            '',
                            'text-white/90 hover:text-secondary-200 hover:bg-white/10',
                            'text-white/90 hover:text-theme-200 hover:bg-white/10'
                          )
                    }`}
                  >
                    {item.name}
                  </Link>
                )}
              </div>
            ))}
          </nav>

          {/* Theme Toggle and CTA Button */}
          <div className="hidden md:flex items-center space-x-4">
            {/* Theme Toggle Button */}
            <button
              onClick={toggleTheme}
              className={`p-2 rounded-full transition-colors duration-300 ${
                isScrolled
                  ? getThemeClasses(
                      'theme-toggle-scrolled',
                      '',
                      'bg-theme-50 text-theme-700 hover:bg-theme-100',
                      'bg-blue-50 text-blue-700 hover:bg-blue-100'
                    )
                  : 'bg-white/10 text-white hover:bg-white/20'
              }`}
              aria-label="切换主题"
            >
              {theme === 'red' ? (
                <Sun className="h-5 w-5" />
              ) : (
                <Moon className="h-5 w-5" />
              )}
            </button>
          </div>

          {/* Mobile menu button */}
          <button
            className={`md:hidden p-2 rounded-lg transition-colors duration-300 ${
              isScrolled
                ? getThemeClasses(
                    'mobile-menu-scrolled',
                    '',
                    'text-medium hover:text-theme-700 hover:bg-theme-50',
                    'text-medium hover:text-blue-700 hover:bg-blue-50'
                  )
                : 'text-white hover:text-secondary-200 hover:bg-white/10'
            }`}
            onClick={() => setIsMobileMenuOpen(!isMobileMenuOpen)}
          >
            {isMobileMenuOpen ? (
              <X className="h-6 w-6" />
            ) : (
              <Menu className="h-6 w-6" />
            )}
          </button>
        </div>

        {/* Mobile Navigation */}
        {isMobileMenuOpen && (
          <div className="md:hidden bg-white/95 backdrop-blur-custom border-t border-secondary-200/50 shadow-custom animate-fade-in-up">
            <div className="px-2 pt-2 pb-3 space-y-1">
              {navigation.map((item) => (
                <div key={item.name}>
                  <Link
                    to={item.href}
                    className={`block px-3 py-3 rounded-lg text-base font-medium ${
                      getThemeClasses(
                        'mobile-link',
                        '',
                        'text-medium hover:text-theme-700 hover:bg-theme-50',
                        'text-medium hover:text-blue-700 hover:bg-blue-50'
                      )
                    } transition-colors duration-200`}
                    onClick={() => setIsMobileMenuOpen(false)}
                  >
                    {item.name}
                  </Link>
                  {item.dropdown && (
                    <div className="pl-4 space-y-1">
                      {item.dropdown.map((subItem) => (
                        <Link
                          key={subItem.name}
                          to={subItem.href}
                          className={`block px-3 py-2 rounded-lg text-sm ${
                            getThemeClasses(
                              'mobile-sublink',
                              '',
                              'text-light hover:text-theme-700 hover:bg-theme-50',
                              'text-light hover:text-blue-700 hover:bg-blue-50'
                            )
                          } transition-colors duration-200`}
                          onClick={() => setIsMobileMenuOpen(false)}
                        >
                          {subItem.name}
                        </Link>
                      ))}
                    </div>
                  )}
                </div>
              ))}
              
              {/* Mobile Theme Toggle */}
              <div className="flex items-center justify-between px-3 py-3">
                <span className="text-medium">切换主题</span>
                <button
                  onClick={toggleTheme}
                  className={`p-2 rounded-full ${
                    theme === 'blue'
                      ? 'bg-blue-50 text-blue-700 hover:bg-blue-100'
                      : 'bg-theme-50 text-theme-700 hover:bg-theme-100'
                  } transition-colors duration-300`}
                  aria-label="切换主题"
                >
                  {theme === 'blue' ? (
                    <Sun className="h-5 w-5" />
                  ) : (
                    <Moon className="h-5 w-5" />
                  )}
                </button>
              </div>
            </div>
          </div>
        )}
      </div>
    </header>
  );
};

export default Header;


