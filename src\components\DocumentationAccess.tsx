import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';
import { FileText, Code, BookOpen, Download, ArrowRight, BookMarked } from 'lucide-react';

const DocumentationAccess: React.FC = () => {
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const resources = [
    {
      icon: <FileText className="h-6 w-6" />,
      title: '产品用户手册',
      description: '详细的产品功能介绍和使用指南，快速上手指导',
      href: '/documentation#manual',
      stats: '50+ 页面'
    },
    {
      icon: <Code className="h-6 w-6" />,
      title: '技术文档',
      description: '核心技术介绍及底层函数说明，开发者必备资源',
      href: '/documentation#technical',
      stats: '100+ 接口'
    },
    {
      icon: <BookOpen className="h-6 w-6" />,
      title: '样例代码',
      description: '完整的示例代码和最佳实践，加速开发进程',
      href: '/documentation#examples',
      stats: '30+ 示例'
    },
    {
      icon: <Download className="h-6 w-6" />,
      title: '资源下载',
      description: '工具包、文档一站式下载中心',
      href: '/documentation#downloads',
      stats: '20+ 资源'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.resource-card, .quick-start-section');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  return (
    <section ref={sectionRef} className="py-24 bg-gray-50 relative overflow-hidden">
      {/* 简约背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-theme-50/40 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-secondary-50/30 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
      </div>

      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
        {/* 标题部分 */}
        <div className="text-center mb-16">
          {/* 标签 */}
          <div className="inline-flex items-center gap-3 px-4 py-2 bg-white rounded-full border border-gray-200 shadow-sm mb-6">
            <BookMarked className="h-4 w-4 text-theme-600" />
            <span className="text-sm font-medium text-gray-700">文档资源</span>
          </div>

          {/* 主标题 */}
          <h2 className="text-3xl lg:text-4xl font-bold text-gray-900 mb-4">
            <span className="text-theme-600">文档</span>中心
          </h2>
          <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            完整的技术文档和资源，帮助您快速上手和深入了解我们的产品
          </p>
        </div>

        {/* 资源卡片网格 */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
          {resources.map((resource, index) => (
            <Link
              key={index}
              to={resource.href}
              data-index={index}
              className={`resource-card group p-8 bg-white rounded-2xl border border-gray-200 hover:border-theme-200 hover:shadow-xl transition-all duration-300 ${
                visibleElements.includes(index) ? 'animate-scale-in' : 'opacity-0'
              }`}
              style={{ animationDelay: `${index * 0.1}s` }}
            >
              {/* 图标 */}
              <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl mb-6 group-hover:bg-theme-100 group-hover:scale-110 transition-all duration-300">
                {resource.icon}
              </div>

              {/* 标题 */}
              <h3 className="text-xl font-bold text-gray-900 mb-3 group-hover:text-theme-700 transition-colors duration-300">
                {resource.title}
              </h3>

              {/* 描述 */}
              <p className="text-gray-600 mb-6 h-14 leading-relaxed text-sm">
                {resource.description}
              </p>

              {/* 统计信息 */}
              <div className="flex items-center justify-between pt-4 border-t border-gray-100">
                <span className="text-sm font-semibold text-theme-600">{resource.stats}</span>
                <ArrowRight className="h-4 w-4 text-gray-400 group-hover:text-theme-600 group-hover:translate-x-1 transition-all duration-300" />
              </div>
            </Link>
          ))}
        </div>
      </div>
    </section>
  );
};

export default DocumentationAccess;