import React from "react";
import {
  ArrowRight,
  Download,
  Zap,
  Globe,
  Code,
  Monitor,
  CheckCircle,
  ExternalLink,
  Target,
  BookOpen,
  FileText,
  Package,
} from "lucide-react";
import { Link } from "react-router-dom";
import OptSuiteArch from "@/assets/optsuite-arch.png";
import HangTianQi from "@/assets/hangtianqi.jpeg";

const OptSuiteDetail: React.FC = () => {

  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              OptSuite - 您的优化问题求解专家
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              具有自主知识产权的高性能数学规划求解器，解决大规模复杂优化决策问题
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <button className="group inline-flex items-center justify-center px-8 py-4 bg-white text-theme-900 font-medium rounded-lg hover:bg-gray-100 transition-all duration-300 shadow-lg">
                <Download className="mr-2 h-5 w-5" />
                <span>免费下载试用</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </button>
            </div>
          </div>
        </div>
      </section>

      {/* 求解器介绍 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧内容 */}
            <div>
              <h2 className="text-3xl font-bold text-gray-900 mb-6">
                新一代工业智能之"芯"OptSuite
              </h2>
              <div className="w-16 h-1 bg-theme-600 mb-6 rounded-full" />
              <p className="text-lg text-gray-600 leading-relaxed">
                OptSuite是具有自主知识产权的高性能、高扩展的数学规划求解器，解决国家战略及产业发展中的大规模复杂优化决策问题，是智能决策场景的"计算芯片"；同时，基于独立研发的核心计算库，为算法开发者提供高效便捷的算法开发框架，构建新一代优化算法科学计算平台。
              </p>
            </div>

            {/* 右侧架构图 */}
            <div className="relative">
              <div className="bg-gradient-to-br from-theme-50 to-secondary-50 rounded-2xl p-8 shadow-lg">
                <img
                  src={OptSuiteArch}
                  alt="OptSuite架构图"
                  className="w-full h-auto"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 产品优势 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">产品优势</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              OptSuite具有显著的技术优势和商业价值，助力企业实现智能化转型
            </p>
          </div>

          {/* 优势总结 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8 mb-12">
            <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <CheckCircle className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">国产自研</h3>
              <p className="text-gray-600">核心算法全栈可控</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">极速求解</h3>
              <p className="text-gray-600">性能领跑行业基准</p>
            </div>
            <div className="bg-white rounded-2xl p-6 shadow-lg text-center">
              <div className="w-16 h-16 bg-theme-100 rounded-full flex items-center justify-center mx-auto mb-4">
                <Globe className="h-8 w-8 text-theme-600" />
              </div>
              <h3 className="text-xl font-bold text-gray-900 mb-2">开放生态</h3>
              <p className="text-gray-600">底层接口灵活调用</p>
            </div>
          </div>

          {/* 详细信息 */}
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {/* 可支持的算法 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 mb-4">
                可支持的算法
              </h3>
              <div className="space-y-2">
                <div className="flex items-center text-gray-600">
                  <div className="w-2 h-2 bg-theme-600 rounded-full mr-3" />
                  <span>线性规划</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <div className="w-2 h-2 bg-theme-600 rounded-full mr-3" />
                  <span>二阶锥规划</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <div className="w-2 h-2 bg-theme-600 rounded-full mr-3" />
                  <span>二次规划</span>
                </div>
              </div>
            </div>

            {/* 适用平台 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 mb-4">适用平台</h3>
              <div className="space-y-2">
                <div className="flex items-center text-gray-600">
                  <Monitor className="h-4 w-4 mr-3 text-theme-600" />
                  <span>Windows</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Monitor className="h-4 w-4 mr-3 text-theme-600" />
                  <span>Linux</span>
                </div>
              </div>
            </div>

            {/* 支持语言 */}
            <div className="bg-white rounded-2xl p-6 shadow-lg">
              <h3 className="text-lg font-bold text-gray-900 mb-4">支持语言</h3>
              <div className="space-y-2">
                <div className="flex items-center text-gray-600">
                  <Code className="h-4 w-4 mr-3 text-theme-600" />
                  <span>C</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Code className="h-4 w-4 mr-3 text-theme-600" />
                  <span>C++</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <Code className="h-4 w-4 mr-3 text-theme-600" />
                  <span>Python</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* OptSuite 求解器应用实例 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">
              OptSuite 求解器应用实例
            </h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              真实案例展示OptSuite在复杂优化问题中的卓越表现
            </p>
          </div>

          {/* 应用实例卡片 */}
          <div className="max-w-4xl mx-auto">
            <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-0">
                {/* 左侧图片 */}
                <div className="relative h-64 lg:h-auto">
                  <img
                    src={HangTianQi}
                    alt="航天器软着陆轨迹优化"
                    className="w-full h-full object-cover"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-transparent" />
                  <div className="absolute bottom-4 left-4 right-4 text-white">
                    <div className="inline-flex items-center px-3 py-1 bg-white/20 backdrop-blur-sm rounded-full text-sm mb-2">
                      <Target className="h-4 w-4 mr-2" />
                      <span>航天应用</span>
                    </div>
                  </div>
                </div>

                {/* 右侧内容 */}
                <div className="p-8">
                  <h3 className="text-2xl font-bold text-gray-900 mb-2">
                    航天器软着陆轨迹优化项目
                  </h3>
                  <p className="text-theme-600 font-medium mb-4">
                    轨迹优化问题
                  </p>
                  <p className="text-gray-600 leading-relaxed mb-6">
                    OptSuite求解器助力航天器软着陆轨迹优化问题，解决航天器在着陆过程中会遇到的各种动力学影响，
                    如引力、大气阻力、风速等，涉及的SOCP问题的优化求解，提升轨迹计算的精度，确保软着陆过程的安全和可靠。
                  </p>

                  <Link
                    to="/cases?case=spacecraft-landing"
                    className="group inline-flex items-center justify-center px-6 py-3 bg-theme-600 text-white font-medium rounded-lg hover:bg-theme-700 transition-all duration-300"
                  >
                    <span>查看详情</span>
                    <ExternalLink className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
                  </Link>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 文档中心 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">文档中心</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
            <p className="text-lg text-gray-600 max-w-2xl mx-auto">
              完整的技术文档和开发资源，助您快速上手OptSuite
            </p>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
            {[
              {
                icon: <BookOpen className="h-6 w-6" />,
                title: "产品用户手册",
                description: "详细的产品使用指南和操作说明",
                filename: "OptSuite-用户手册.pdf",
              },
              {
                icon: <FileText className="h-6 w-6" />,
                title: "技术文档",
                description: "深入的技术原理和架构说明",
                filename: "OptSuite-技术文档.pdf",
              },
              {
                icon: <Code className="h-6 w-6" />,
                title: "样例代码",
                description: "丰富的代码示例和最佳实践",
                filename: "OptSuite-样例代码.pdf",
              },
              {
                icon: <Package className="h-6 w-6" />,
                title: "资源下载",
                description: "SDK、工具包和相关资源",
                filename: "optSuite-kit.zip",
              },
            ].map((doc, index) => (
              <div
                key={index}
                className="bg-white rounded-2xl p-6 shadow-lg hover:shadow-xl transition-all duration-300 group"
              >
                <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl mb-4 group-hover:bg-theme-100 transition-colors duration-300">
                  {doc.icon}
                </div>
                <h3 className="text-lg font-semibold text-gray-900 mb-3">
                  {doc.title}
                </h3>
                <p className="text-gray-600 text-sm leading-relaxed mb-4">
                  {doc.description}
                </p>
                <button
                  onClick={() => {
                    // 模拟PDF下载
                    const link = document.createElement("a");
                    link.href = "#"; // 这里应该是实际的PDF文件路径
                    link.download = doc.filename;
                    link.click();
                  }}
                  className="group/btn inline-flex items-center text-theme-600 text-sm font-medium hover:text-theme-700 transition-colors duration-300"
                >
                  <Download className="mr-2 h-4 w-4" />
                  <span>下载</span>
                  <ArrowRight className="ml-1 h-4 w-4 group-hover/btn:translate-x-1 transition-transform duration-300" />
                </button>
              </div>
            ))}
          </div>
        </div>
      </section>
    </div>
  );
};

export default OptSuiteDetail;
