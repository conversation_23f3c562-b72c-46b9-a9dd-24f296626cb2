import React from 'react';
import { use<PERSON>ara<PERSON>, Link } from 'react-router-dom';
import { CheckCircle, TrendingUp, Clock, Users, ArrowRight } from 'lucide-react';
import BackgroundDecorations from '../components/BackgroundDecorations';

const SolutionDetail: React.FC = () => {
  const { solutionId } = useParams<{ solutionId: string }>();

  const solutionData = {
    'supply-chain-optimization': {
      title: '经营端 - 供应链协同优化',
      client: 'ABC制造集团',
      industry: '制造业',
      duration: '6个月',
      teamSize: '8人',
      improvement: '库存成本降低35%',
      image: 'https://images.pexels.com/photos/3184160/pexels-photo-3184160.jpeg?auto=compress&cs=tinysrgb&w=1200',
      challenges: [
        '库存成本过高，资金占用严重',
        '供应商协调困难，交货不及时',
        '需求预测不准确，库存积压严重',
        '供应链各环节缺乏有效协调'
      ],
      solution: [
        '建立智能需求预测系统，提高预测准确性',
        '优化库存策略，降低库存水平',
        '构建供应商协同平台，提升协调效率',
        '实施端到端可视化管理'
      ],
      results: [
        { metric: '库存成本', improvement: '降低35%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '库存周转率', improvement: '提升50%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '供应商交货', improvement: '准时率95%', icon: <Clock className="h-5 w-5" /> },
        { metric: '缺货率', improvement: '降低80%', icon: <CheckCircle className="h-5 w-5" /> }
      ],
      testimonial: {
        quote: "OptSuite的供应链优化解决方案帮助我们显著提升了运营效率，库存成本大幅降低，同时保证了供应的稳定性。",
        author: "张总",
        position: "供应链总监",
        avatar: "https://images.pexels.com/photos/3184639/pexels-photo-3184639.jpeg?auto=compress&cs=tinysrgb&w=200"
      }
    },
    'smart-scheduling': {
      title: '生产端 - 智能排产排程',
      client: 'XYZ工厂',
      industry: '制造业',
      duration: '4个月',
      teamSize: '6人',
      improvement: '生产效率提升45%',
      image: 'https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=1200',
      challenges: [
        '生产计划调整频繁，效率低下',
        '设备利用率不高，产能浪费',
        '人工排产无法处理复杂约束',
        '生产进度难以实时跟踪'
      ],
      solution: [
        '部署智能排产系统，自动生成最优排产方案',
        '实时监控生产进度，及时调整计划',
        '优化设备和人员配置，提高利用率',
        '建立生产数据分析平台'
      ],
      results: [
        { metric: '生产效率', improvement: '提升45%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '设备利用率', improvement: '提升30%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '准时交付率', improvement: '达到95%', icon: <Clock className="h-5 w-5" /> },
        { metric: '生产成本', improvement: '降低20%', icon: <CheckCircle className="h-5 w-5" /> }
      ],
      testimonial: {
        quote: "智能排产系统的实施让我们的生产管理水平大幅提升，生产效率显著改善，成本也得到了有效控制。",
        author: "李经理",
        position: "生产经理",
        avatar: "https://images.pexels.com/photos/3184465/pexels-photo-3184465.jpeg?auto=compress&cs=tinysrgb&w=200"
      }
    },
    'workforce-optimization': {
      title: '物流端 - 资源调度优化',
      client: 'DEF服务公司',
      industry: '服务业',
      duration: '3个月',
      teamSize: '5人',
      improvement: '人力成本节约30%',
      image: 'https://images.pexels.com/photos/3184339/pexels-photo-3184339.jpeg?auto=compress&cs=tinysrgb&w=1200',
      challenges: [
        '人员配置不合理，效率低下',
        '工作负荷分配不均，影响士气',
        '人力成本持续上升',
        '员工技能与岗位匹配度不高'
      ],
      solution: [
        '建立人员技能评估体系',
        '优化岗位配置和工作流程',
        '实施智能排班系统',
        '建立绩效监控和分析平台'
      ],
      results: [
        { metric: '人力成本', improvement: '节约30%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '工作效率', improvement: '提升40%', icon: <TrendingUp className="h-5 w-5" /> },
        { metric: '员工满意度', improvement: '提升25%', icon: <Users className="h-5 w-5" /> },
        { metric: '人员流失率', improvement: '降低50%', icon: <CheckCircle className="h-5 w-5" /> }
      ],
      testimonial: {
        quote: "人力资源优化项目帮助我们大幅提升了人力资源管理效率，成本控制效果显著，员工满意度也得到了提升。",
        author: "王主任",
        position: "人力资源总监",
        avatar: "https://images.pexels.com/photos/3184418/pexels-photo-3184418.jpeg?auto=compress&cs=tinysrgb&w=200"
      }
    }
  };

  const solution = solutionData[solutionId as keyof typeof solutionData];

  if (!solution) {
    return (
      <div className="pt-16 min-h-screen flex items-center justify-center bg-gradient-to-br from-warm-50 to-white">
        <div className="text-center">
          <h1 className="text-4xl font-bold text-dark mb-4">解决方案未找到</h1>
          <Link to="/solutions" className="text-theme-600 hover:text-theme-700">
            返回解决方案列表
          </Link>
        </div>
      </div>
    );
  }

  return (
    <div className="relative overflow-hidden">
      {/* Background Decorations */}
      <BackgroundDecorations />

      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-40 pb-24 relative overflow-hidden">
        <BackgroundDecorations particleCount={18} />
        <div className="relative h-128 overflow-hidden">
          <img
            src={solution.image}
            alt={solution.title}
            className="w-full h-full object-cover"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-theme-900/80 via-theme-800/60 to-theme-700/40" />
        </div>
        
        <div className="absolute inset-0 flex items-center">
          <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-white">
            <div className="max-w-3xl animate-slide-in-left">
              <h1 className="text-5xl font-bold mb-6">{solution.title}</h1>
              <div className="flex flex-wrap gap-4 mb-6">
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                  <span className="text-sm font-medium">客户：{solution.client}</span>
                </div>
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                  <span className="text-sm font-medium">行业：{solution.industry}</span>
                </div>
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                  <Clock className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">周期：{solution.duration}</span>
                </div>
                <div className="flex items-center bg-white/20 backdrop-blur-sm rounded-lg px-4 py-2 border border-white/30">
                  <Users className="h-4 w-4 mr-1" />
                  <span className="text-sm font-medium">团队：{solution.teamSize}</span>
                </div>
              </div>
              <div className="text-2xl font-bold text-secondary-200 bg-gradient-to-r from-secondary-400 to-secondary-500 bg-clip-text text-transparent">
                {solution.improvement}
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16 relative z-10">
        <div className="grid grid-cols-1 lg:grid-cols-2 gap-12">
          {/* Challenges */}
          <div className="space-y-8 animate-slide-in-left">
            <div>
              <h2 className="text-3xl font-bold text-dark mb-6">挑战与痛点</h2>
              <div className="space-y-4">
                {solution.challenges.map((challenge, index) => (
                  <div key={index} className="flex items-start group hover-lift" style={{ animationDelay: `${index * 0.1}s` }}>
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-theme-100 to-theme-200 rounded-full flex items-center justify-center mr-4 mt-1 group-hover:scale-110 transition-transform duration-300">
                      <div className="w-3 h-3 bg-theme-600 rounded-full"></div>
                    </div>
                    <p className="text-medium leading-relaxed group-hover:text-dark transition-colors duration-300">{challenge}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Solution */}
            <div>
              <h2 className="text-3xl font-bold text-dark mb-6">解决方案</h2>
              <div className="space-y-4">
                {solution.solution.map((item, index) => (
                  <div key={index} className="flex items-start group hover-lift" style={{ animationDelay: `${index * 0.1}s` }}>
                    <div className="flex-shrink-0 w-8 h-8 bg-gradient-to-r from-secondary-100 to-secondary-200 rounded-full flex items-center justify-center mr-4 mt-1 group-hover:scale-110 transition-transform duration-300">
                      <CheckCircle className="w-5 h-5 text-secondary-700" />
                    </div>
                    <p className="text-medium leading-relaxed group-hover:text-dark transition-colors duration-300">{item}</p>
                  </div>
                ))}
              </div>
            </div>
          </div>

          {/* Results */}
          <div className="space-y-8 animate-slide-in-right">
            <div>
              <h2 className="text-3xl font-bold text-dark mb-6">价值与成效</h2>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                {solution.results.map((result, index) => (
                  <div key={index} className="card-enhanced p-6 text-center hover-lift group" style={{ animationDelay: `${index * 0.1}s` }}>
                    <div className="inline-flex items-center justify-center w-16 h-16 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-xl mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                      <div className="text-white">
                        {result.icon}
                      </div>
                    </div>
                    <h3 className="text-lg font-semibold text-dark mb-2 group-hover:text-theme-700 transition-colors duration-300">{result.metric}</h3>
                    <p className="text-2xl font-bold text-primary">{result.improvement}</p>
                  </div>
                ))}
              </div>
            </div>

            {/* Testimonial */}
            <div className="bg-gradient-to-r from-theme-50 to-secondary-50 rounded-2xl p-8 border border-secondary-200/50 shadow-lg hover-lift">
              <h3 className="text-2xl font-bold text-dark mb-6">客户评价</h3>
              <div className="flex items-start space-x-4">
                <img
                  src={solution.testimonial.avatar}
                  alt={solution.testimonial.author}
                  className="w-16 h-16 rounded-full object-cover flex-shrink-0 ring-4 ring-secondary-200 hover:ring-secondary-400 transition-all duration-300"
                />
                <div>
                  <blockquote className="text-medium italic mb-4 leading-relaxed text-lg">
                    "{solution.testimonial.quote}"
                  </blockquote>
                  <div>
                    <div className="font-semibold text-dark text-lg">{solution.testimonial.author}</div>
                    <div className="text-sm text-theme-600 font-medium">{solution.testimonial.position}</div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Related Products */}
      <section className="bg-gradient-to-br from-warm-50 to-theme-50 py-16 relative overflow-hidden">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <h2 className="text-3xl font-bold text-dark text-center mb-12">相关产品推荐</h2>
          <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
            {['OptSuite', 'OptAPS', 'OptPinnacle'].map((product, index) => (
              <Link
                key={index}
                to={`/products/${product.toLowerCase()}`}
                className="card-enhanced p-6 hover-lift group transition-all duration-300 transform hover:-translate-y-2"
                style={{ animationDelay: `${index * 0.1}s` }}
              >
                <div className="w-16 h-16 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-xl flex items-center justify-center mx-auto mb-4 group-hover:scale-110 group-hover:rotate-3 transition-all duration-300 shadow-lg">
                  <span className="text-white font-bold text-xl">{product.charAt(0)}</span>
                </div>
                <h3 className="text-xl font-bold text-dark mb-2 group-hover:text-theme-700 transition-colors duration-300">{product}</h3>
                <p className="text-medium mb-4">了解更多关于{product}的功能特性</p>
                <div className="flex items-center text-theme-600 group-hover:text-theme-700 transition-colors duration-300">
                  <span className="mr-2">查看产品</span>
                  <ArrowRight className="h-4 w-4 group-hover:translate-x-1 transition-transform duration-300" />
                </div>
              </Link>
            ))}
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="bg-gradient-to-r from-theme-600 via-theme-700 to-secondary-600 py-16 relative overflow-hidden">
        {/* Background Pattern */}
        <div className="absolute inset-0 opacity-10">
          <div className="absolute top-0 left-0 w-32 h-32 border border-white/30 rounded-full animate-float" />
          <div className="absolute bottom-0 right-0 w-24 h-24 bg-white/20 rounded-lg rotate-45 animate-pulse" />
          <div className="absolute top-1/2 left-1/4 w-16 h-16 border-2 border-white/20 rotate-12" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 text-center relative z-10">
          <h2 className="text-3xl font-bold text-white mb-4">
            想了解更多关于此解决方案的信息？
          </h2>
          <p className="text-xl text-theme-100 mb-8">
            联系我们的专家团队，获取详细的方案介绍和案例分析
          </p>
          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Link
              to="/contact"
              className="group inline-flex items-center px-8 py-3 bg-white text-theme-600 font-medium rounded-lg hover:bg-gray-100 transition-all duration-200 shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden"
            >
              <span className="relative z-10">立即咨询</span>
              <ArrowRight className="ml-2 h-5 w-5 group-hover:translate-x-1 transition-transform relative z-10" />
              <div className="absolute inset-0 bg-gradient-to-r from-theme-50/0 via-theme-50/50 to-theme-50/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
            </Link>
            <Link
              to="/solutions"
              className="inline-flex items-center px-8 py-3 border border-white/30 text-white font-medium rounded-lg hover:bg-white/10 transition-all duration-200"
            >
              查看更多案例
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default SolutionDetail;
