import React, { createContext, useState, useEffect, ReactNode } from 'react';

type Theme = 'red' | 'blue';

interface ThemeContextType {
  theme: Theme;
  toggleTheme: () => void;
}

export const ThemeContext = createContext<ThemeContextType>({
  theme: 'red',
  toggleTheme: () => {},
});

interface ThemeProviderProps {
  children: ReactNode;
}

export const useTheme = () => React.useContext(ThemeContext);

export const ThemeProvider: React.FC<ThemeProviderProps> = ({ children }) => {
  // 从localStorage获取主题，如果没有则默认为红色主题
  const [theme, setTheme] = useState<Theme>(() => {
    const savedTheme = localStorage.getItem('theme');
    return (savedTheme as Theme) || 'red';
  });

  // 切换主题
  const toggleTheme = () => {
    setTheme(prevTheme => prevTheme === 'red' ? 'blue' : 'red');
  };

  // 当主题变化时，更新localStorage和文档根元素的data-theme属性
  useEffect(() => {
    localStorage.setItem('theme', theme);
    document.documentElement.setAttribute('data-theme', theme);
    
    // 不再需要手动设置CSS变量，因为我们使用CSS选择器来应用它们
  }, [theme]);

  return (
    <ThemeContext.Provider value={{ theme, toggleTheme }}>
      {children}
    </ThemeContext.Provider>
  );
};

