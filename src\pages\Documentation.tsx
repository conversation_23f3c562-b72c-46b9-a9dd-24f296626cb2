import React, { useState, useEffect, useRef } from 'react';
import { Search, FileText, Code, BookOpen, Download, Copy, Check, Zap, Sparkles } from 'lucide-react';
import BackgroundDecorations from '../components/BackgroundDecorations';

const Documentation: React.FC = () => {
  const [searchTerm, setSearchTerm] = useState('');
  const [activeCategory, setActiveCategory] = useState('manual');
  const [copiedCode, setCopiedCode] = useState<string | null>(null);
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const categories = [
    { 
      id: 'manual', 
      name: '产品说明书', 
      icon: <FileText className="h-5 w-5" />,
      color: 'from-theme-600 to-theme-700',
      bgColor: 'from-theme-50 to-theme-100'
    },
    { 
      id: 'technical', 
      name: '技术文档', 
      icon: <Code className="h-5 w-5" />,
      color: 'from-secondary-600 to-secondary-700',
      bgColor: 'from-secondary-50 to-secondary-100'
    },
    { 
      id: 'examples', 
      name: '样例代码', 
      icon: <BookOpen className="h-5 w-5" />,
      color: 'from-theme-800 to-secondary-600',
      bgColor: 'from-warm-50 to-warm-100'
    },
    { 
      id: 'downloads', 
      name: '资源下载', 
      icon: <Download className="h-5 w-5" />,
      color: 'from-secondary-700 to-theme-600',
      bgColor: 'from-secondary-50 to-theme-50'
    }
  ];

  const manualDocs = [
    {
      title: 'OptSuite 用户手册',
      description: '详细介绍OptSuite的功能特性和使用方法',
      category: 'OptSuite',
      lastUpdated: '2024-01-15',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    },
    {
      title: 'OptAPS 操作指南',
      description: '生产排程系统的完整操作指南',
      category: 'OptAPS',
      lastUpdated: '2024-01-10',
      color: 'from-secondary-600 to-secondary-700',
      bgGradient: 'from-secondary-50 to-secondary-100'
    },
    {
      title: 'OptPinnacle 配置手册',
      description: '企业级解决方案的配置和部署指南',
      category: 'OptPinnacle',
      lastUpdated: '2024-01-20',
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100'
    }
  ];

  const technicalDocs = [
    {
      title: 'REST API 文档',
      description: '完整的API接口文档和使用示例',
      category: 'API',
      lastUpdated: '2024-01-18',
      color: 'from-theme-600 to-theme-700',
      bgGradient: 'from-theme-50 to-theme-100'
    },
    {
      title: 'SDK 开发指南',
      description: '各语言SDK的使用指南和最佳实践',
      category: 'SDK',
      lastUpdated: '2024-01-12',
      color: 'from-secondary-600 to-secondary-700',
      bgGradient: 'from-secondary-50 to-secondary-100'
    },
    {
      title: '数据库设计规范',
      description: '数据库表结构和设计规范说明',
      category: 'Database',
      lastUpdated: '2024-01-08',
      color: 'from-theme-800 to-secondary-600',
      bgGradient: 'from-warm-50 to-warm-100'
    }
  ];

  const codeExamples = [
    {
      title: 'Python SDK 基础使用',
      description: '使用Python SDK进行基本的优化任务',
      language: 'Python',
      code: `from optsuite import OptimizationEngine

# 初始化优化引擎
engine = OptimizationEngine(api_key="your_api_key")

# 创建优化任务
task = engine.create_task({
    "type": "supply_chain",
    "data": data,
    "constraints": constraints
})

# 执行优化
result = task.optimize()
print(f"优化结果: {result.objective_value}")`,
      color: 'from-theme-600 to-theme-700'
    },
    {
      title: '数据分析示例',
      description: '使用我们的分析工具进行数据处理',
      language: 'Python',
      code: `from optsuite.analytics import DataAnalyzer

# 创建分析器
analyzer = DataAnalyzer()

# 加载数据
data = analyzer.load_data("data.csv")
      
# 执行分析
results = analyzer.analyze(data, {
    "metrics": ["efficiency", "cost"],
    "groupby": "department"
})

# 生成报告
report = analyzer.generate_report(results)
print(report)`,
      color: 'from-secondary-600 to-secondary-700'
    }
  ];

  const downloads = [
    {
      title: 'OptSuite SDK (Python)',
      description: 'Python SDK包，包含完整的API封装',
      size: '2.5 MB',
      version: 'v2.1.0',
      downloadUrl: '#',
      color: 'from-theme-600 to-theme-700'
    },
    {
      title: 'OptAPS 配置模板',
      description: '常用的生产排程配置模板',
      size: '1.2 MB',
      version: 'v1.5.0',
      downloadUrl: '#',
      color: 'from-secondary-600 to-secondary-700'
    },
    {
      title: '数据导入工具',
      description: 'Excel数据导入工具和模板',
      size: '5.8 MB',
      version: 'v1.3.0',
      downloadUrl: '#',
      color: 'from-theme-800 to-secondary-600'
    }
  ];

  useEffect(() => {
    const observer = new IntersectionObserver(
      (entries) => {
        entries.forEach((entry) => {
          if (entry.isIntersecting) {
            const index = parseInt(entry.target.getAttribute('data-index') || '0');
            setVisibleElements(prev => [...prev, index]);
          }
        });
      },
      { threshold: 0.1 }
    );

    const elements = sectionRef.current?.querySelectorAll('.fade-in-section');
    elements?.forEach((element) => observer.observe(element));

    return () => observer.disconnect();
  }, []);

  const copyCode = (code: string, title: string) => {
    navigator.clipboard.writeText(code);
    setCopiedCode(title);
    setTimeout(() => setCopiedCode(null), 2000);
  };

  // Generate floating particles
  const particles = Array.from({ length: 30 }, (_, i) => ({
    id: i,
    size: Math.random() * 6 + 2,
    left: Math.random() * 100,
    top: Math.random() * 100,
    delay: Math.random() * 4,
    duration: Math.random() * 3 + 4,
  }));

  const renderContent = () => {
    switch (activeCategory) {
      case 'manual':
        return (
          <div className="space-y-6">
            {manualDocs.map((doc, index) => (
              <div key={index} className="group" data-index={index + 10}>
                <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 transform hover:-translate-y-1">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-4">
                        <div className={`p-3 bg-gradient-to-r ${doc.color} rounded-xl mr-4`}>
                          <FileText className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-dark group-hover:text-theme-700 transition-colors">
                            {doc.title}
                          </h3>
                          <span className={`inline-block bg-gradient-to-r ${doc.bgGradient} text-theme-700 px-3 py-1 rounded-full text-sm font-medium mt-1`}>
                            {doc.category}
                          </span>
                        </div>
                      </div>
                      <p className="text-medium mb-4 leading-relaxed">{doc.description}</p>
                      <div className="flex items-center text-sm text-light">
                        <span>更新时间: {doc.lastUpdated}</span>
                      </div>
                    </div>
                    <button className={`bg-gradient-to-r ${doc.color} text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium`}>
                      阅读文档
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'technical':
        return (
          <div className="space-y-6">
            {technicalDocs.map((doc, index) => (
              <div key={index} className="group" data-index={index + 13}>
                <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 transform hover:-translate-y-1">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-4">
                        <div className={`p-3 bg-gradient-to-r ${doc.color} rounded-xl mr-4`}>
                          <Code className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-dark group-hover:text-secondary-700 transition-colors">
                            {doc.title}
                          </h3>
                          <span className={`inline-block bg-gradient-to-r ${doc.bgGradient} text-secondary-700 px-3 py-1 rounded-full text-sm font-medium mt-1`}>
                            {doc.category}
                          </span>
                        </div>
                      </div>
                      <p className="text-medium mb-4 leading-relaxed">{doc.description}</p>
                      <div className="flex items-center text-sm text-light">
                        <span>更新时间: {doc.lastUpdated}</span>
                      </div>
                    </div>
                    <button className={`bg-gradient-to-r ${doc.color} text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium`}>
                      查看API
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'examples':
        return (
          <div className="space-y-8">
            {codeExamples.map((example, index) => (
              <div key={index} data-index={index + 16}>
                <div className="bg-white rounded-2xl shadow-lg overflow-hidden border border-gray-100 hover:shadow-xl transition-all duration-300">
                  <div className="p-8 border-b border-gray-100">
                    <div className="flex items-center justify-between">
                      <div className="flex items-center">
                        <div className={`p-3 bg-gradient-to-r ${example.color} rounded-xl mr-4`}>
                          <BookOpen className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-dark mb-2">{example.title}</h3>
                          <p className="text-medium">{example.description}</p>
                        </div>
                      </div>
                      <span className="bg-gray-100 text-gray-700 px-4 py-2 rounded-full text-sm font-medium">
                        {example.language}
                      </span>
                    </div>
                  </div>
                  <div className="relative">
                    <pre className="bg-gray-900 text-gray-100 p-8 overflow-x-auto text-sm leading-relaxed">
                      <code>{example.code}</code>
                    </pre>
                    <button
                      onClick={() => copyCode(example.code, example.title)}
                      className="absolute top-4 right-4 bg-white/10 hover:bg-white/20 text-white p-2 rounded-lg transition-colors backdrop-blur-sm"
                    >
                      {copiedCode === example.title ? (
                        <Check className="h-4 w-4" />
                      ) : (
                        <Copy className="h-4 w-4" />
                      )}
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      
      case 'downloads':
        return (
          <div className="space-y-6">
            {downloads.map((download, index) => (
              <div key={index} className="group" data-index={index + 18}>
                <div className="bg-white rounded-2xl shadow-lg p-8 hover:shadow-xl transition-all duration-300 border border-gray-100 transform hover:-translate-y-1">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center mb-4">
                        <div className={`p-3 bg-gradient-to-r ${download.color} rounded-xl mr-4`}>
                          <Download className="h-6 w-6 text-white" />
                        </div>
                        <div>
                          <h3 className="text-xl font-bold text-dark group-hover:text-theme-700 transition-colors">
                            {download.title}
                          </h3>
                          <div className="flex items-center space-x-4 mt-1">
                            <span className="text-sm text-light">大小: {download.size}</span>
                            <span className="text-sm text-light">版本: {download.version}</span>
                          </div>
                        </div>
                      </div>
                      <p className="text-medium leading-relaxed">{download.description}</p>
                    </div>
                    <button className={`bg-gradient-to-r ${download.color} text-white px-6 py-3 rounded-xl hover:shadow-lg transition-all duration-300 font-medium flex items-center`}>
                      <Download className="h-4 w-4 mr-2" />
                      下载
                    </button>
                  </div>
                </div>
              </div>
            ))}
          </div>
        );
      
      default:
        return null;
    }
  };

  return (
    <div ref={sectionRef} className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-40 pb-24 relative overflow-hidden">
        <BackgroundDecorations />
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center">
            <div className="inline-flex items-center space-x-3 bg-white/10 backdrop-blur-sm rounded-full px-6 py-3 mb-6 border border-white/20 shadow-lg">
              <div className="p-2 bg-gradient-to-r from-theme-600 to-secondary-500 rounded-full">
                <FileText className="h-5 w-5 text-white" />
              </div>
              <span className="text-secondary-200 font-medium">技术文档</span>
              <Sparkles className="h-5 w-5 text-secondary-300 animate-pulse" />
            </div>
            <h1 className="text-4xl md:text-5xl font-bold mb-6">
              <span className="text-secondary">技术文档</span>
              <br />
              <span className="text-white">资源中心</span>
            </h1>
            <div className="w-24 h-1 bg-gradient-to-r from-theme-600 to-secondary-500 mx-auto mb-8 rounded-full" />
            <p className="text-xl text-theme-100 max-w-3xl mx-auto mb-10 leading-relaxed">
              完整的技术文档和资源，帮助您快速上手和深入了解我们的产品
            </p>
            
            {/* Search */}
            <div className="max-w-md mx-auto relative">
              <div className="relative">
                <Search className="absolute left-4 top-1/2 transform -translate-y-1/2 h-5 w-5 text-gray-400" />
                <input
                  type="text"
                  placeholder="搜索文档..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="w-full pl-12 pr-4 py-4 bg-white/10 backdrop-blur-sm border border-white/20 rounded-xl text-white placeholder-gray-300 focus:outline-none focus:ring-2 focus:ring-secondary-500 transition-all"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-16">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="bg-white rounded-2xl shadow-lg p-6 sticky top-24 border border-gray-100">
              <h2 className="text-lg font-semibold text-dark mb-6">文档分类</h2>
              <nav className="space-y-2">
                {categories.map((category) => (
                  <button
                    key={category.id}
                    onClick={() => setActiveCategory(category.id)}
                    className={`w-full flex items-center px-4 py-3 rounded-xl font-medium transition-all duration-300 ${
                      activeCategory === category.id
                        ? `bg-gradient-to-r ${category.bgColor} text-theme-700 shadow-sm`
                        : 'text-medium hover:bg-gray-50'
                    }`}
                  >
                    <div className={`p-2 rounded-lg mr-3 ${
                      activeCategory === category.id 
                        ? `bg-gradient-to-r ${category.color} text-white` 
                        : 'bg-gray-100 text-gray-600'
                    }`}>
                      {category.icon}
                    </div>
                    <span>{category.name}</span>
                  </button>
                ))}
              </nav>
            </div>
          </div>

          {/* Content */}
          <div className="lg:col-span-3">
            {renderContent()}
          </div>
        </div>
      </div>
    </div>
  );
};

export default Documentation;





