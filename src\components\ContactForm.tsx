import React, { useState, useEffect, useRef } from 'react';
import { Mail, Phone, MapPin, Send, Clock, CheckCircle, Spark<PERSON>, Zap } from 'lucide-react';
import BackgroundDecorations from './BackgroundDecorations';

const ContactForm: React.FC = () => {
  const [formData, setFormData] = useState({
    name: '',
    email: '',
    phone: '',
    company: '',
    subject: '',
    message: ''
  });
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [isSubmitted, setIsSubmitted] = useState(false);
  const [visibleElements, setVisibleElements] = useState<number[]>([]);
  const sectionRef = useRef<HTMLDivElement>(null);

  const handleChange = (e: React.ChangeEvent<HTMLInputElement | HTMLTextAreaElement | HTMLSelectElement>) => {
    setFormData({
      ...formData,
      [e.target.name]: e.target.value
    });
  };

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    setIsSubmitting(true);
    
    // Simulate form submission
    setTimeout(() => {
      setIsSubmitting(false);
      setIsSubmitted(true);
      setFormData({
        name: '',
        email: '',
        phone: '',
        company: '',
        subject: '',
        message: ''
      });
    }, 2000);
  };

  if (isSubmitted) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 flex items-center justify-center relative overflow-hidden">
        <BackgroundDecorations particleCount={15} particleOpacity={60} />
        
        <div className="max-w-md mx-auto bg-white rounded-2xl shadow-2xl p-8 text-center relative z-10 border border-gray-100">
          <div className="w-20 h-20 bg-gradient-to-r from-green-500 to-green-600 rounded-full flex items-center justify-center mx-auto mb-6 shadow-lg">
            <CheckCircle className="h-10 w-10 text-white" />
          </div>
          <h2 className="text-3xl font-bold text-dark mb-4">提交成功！</h2>
          <p className="text-medium mb-8 leading-relaxed">
            感谢您的咨询，我们会在24小时内与您联系。
          </p>
          <button
            onClick={() => setIsSubmitted(false)}
            className="w-full bg-gradient-to-r from-theme-600 to-theme-700 text-white py-4 rounded-xl hover:from-theme-700 hover:to-theme-800 transition-all duration-300 font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1"
          >
            继续咨询
          </button>
        </div>
      </div>
    );
  }

  return (
    <div ref={sectionRef} className="min-h-screen bg-gray-50">
      {/* Hero Section */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-theme-900 text-white pt-40 pb-24 relative overflow-hidden">
        <BackgroundDecorations particleCount={15} particleOpacity={60} />
        
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            <span className="text-secondary-200">专业咨询</span>
            <br />
            <span className="text-secondary-200">立即获取专业解决方案</span>
          </h1>
          <div className="flex items-center space-x-4">
            <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4">
              <Phone className="h-5 w-5 text-secondary-300" />
            </div>
            <div>
              <h3 className="text-xl font-semibold text-dark mb-3">办公地址</h3>
              <p className="text-sm text-light">欢迎预约参观</p>
            </div>
          </div>
        </div>
      </section>

      {/* Contact Form */}
      <section className="py-24 bg-gradient-to-br from-warm-50 via-white to-theme-50 relative overflow-hidden">
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-theme-500/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-secondary-500/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-6xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="bg-white rounded-3xl shadow-2xl overflow-hidden border border-gray-100">
            <div className="grid grid-cols-1 lg:grid-cols-2">
              {/* Form */}
              <div className="p-8 lg:p-12" data-index="3">
                <div className="inline-flex items-center space-x-2 bg-theme-100 rounded-full px-4 py-2 mb-6">
                  <div className="w-2 h-2 bg-theme-600 rounded-full animate-pulse" />
                  <span className="text-theme-700 font-medium text-sm">免费咨询</span>
                </div>
                <h2 className="text-3xl md:text-4xl font-bold text-dark mb-8">
                  立即<span className="text-theme-700">联系我们</span>
                </h2>
                <form onSubmit={handleSubmit}>
                  <div>
                    <label htmlFor="name" className="block text-sm font-medium text-dark mb-2">
                      姓名 *
                    </label>
                    <input
                      type="text"
                      id="name"
                      name="name"
                      value={formData.name}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white"
                      placeholder="请输入您的姓名"
                    />
                  </div>

                  <div>
                    <label htmlFor="email" className="block text-sm font-medium text-dark mb-2">
                      邮箱 *
                    </label>
                    <input
                      type="email"
                      id="email"
                      name="email"
                      value={formData.email}
                      onChange={handleChange}
                      required
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white"
                      placeholder="请输入您的邮箱"
                    />
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-2 gap-6">
                    <div>
                      <label htmlFor="phone" className="block text-sm font-medium text-dark mb-2">
                        电话
                      </label>
                      <input
                        type="tel"
                        id="phone"
                        name="phone"
                        value={formData.phone}
                        onChange={handleChange}
                        className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white"
                        placeholder="请输入您的电话"
                      />
                    </div>
                    
                    <div>
                      <label htmlFor="company" className="block text-sm font-medium text-dark mb-2">
                        公司名称
                      </label>
                      <input
                        type="text"
                        id="company"
                        name="company"
                        value={formData.company}
                        onChange={handleChange}
                        className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white"
                        placeholder="请输入公司名称"
                      />
                    </div>
                  </div>

                  <div>
                    <label htmlFor="subject" className="block text-sm font-medium text-dark mb-2">
                      咨询主题
                    </label>
                    <select
                      id="subject"
                      name="subject"
                      value={formData.subject}
                      onChange={handleChange}
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white"
                    >
                      <option value="">请选择咨询主题</option>
                      <option value="product">产品咨询</option>
                      <option value="solution">解决方案</option>
                      <option value="technical">技术支持</option>
                      <option value="cooperation">合作洽谈</option>
                      <option value="other">其他</option>
                    </select>
                  </div>

                  <div>
                    <label htmlFor="message" className="block text-sm font-medium text-dark mb-2">
                      详细描述 *
                    </label>
                    <textarea
                      id="message"
                      name="message"
                      value={formData.message}
                      onChange={handleChange}
                      required
                      rows={4}
                      className="w-full px-4 py-4 border border-gray-200 rounded-xl focus:outline-none focus:ring-2 focus:ring-theme-500 focus:border-transparent transition-all duration-300 bg-gray-50 hover:bg-white resize-none"
                      placeholder="请详细描述您的需求..."
                    />
                  </div>

                  <button
                    type="submit"
                    disabled={isSubmitting}
                    className="w-full bg-gradient-to-r from-theme-600 to-theme-700 text-white py-4 rounded-xl hover:from-theme-700 hover:to-theme-800 transition-all duration-300 disabled:opacity-50 disabled:cursor-not-allowed flex items-center justify-center font-semibold shadow-lg hover:shadow-xl transform hover:-translate-y-1 relative overflow-hidden group"
                  >
                    {isSubmitting ? (
                      <>
                        <div className="animate-spin rounded-full h-5 w-5 border-b-2 border-white mr-2"></div>
                        提交中...
                      </>
                    ) : (
                      <>
                        <span className="relative z-10">立即提交</span>
                        <Send className="h-5 w-5 ml-2 group-hover:translate-x-1 transition-transform relative z-10" />
                        <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 transform -skew-x-12 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />
                      </>
                    )}
                  </button>
                </form>
              </div>

              {/* Info Panel */}
              <div className="bg-gradient-to-br from-theme-600 to-theme-700 p-8 lg:p-12 text-white relative overflow-hidden">
                {/* Background Decorations */}
                <div className="absolute inset-0">
                  <div className="absolute top-0 right-0 w-64 h-64 bg-gradient-to-bl from-secondary-500/20 to-transparent rounded-full translate-x-1/2 -translate-y-1/2" />
                  <div className="absolute bottom-0 left-0 w-64 h-64 bg-gradient-to-tr from-theme-800/30 to-transparent rounded-full -translate-x-1/2 translate-y-1/2" />
                </div>

                <div className="relative z-10">
                  <div className="inline-flex items-center space-x-2 bg-white/10 backdrop-blur-sm rounded-full px-4 py-2 mb-8">
                    <Zap className="h-4 w-4 text-secondary-300" />
                    <span className="text-secondary-200 font-medium text-sm">为什么选择我们</span>
                  </div>
                  <h3 className="text-2xl md:text-3xl font-bold mb-8">专业服务保障</h3>
                  
                  <div className="space-y-6">
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <CheckCircle className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-lg">专业团队</h4>
                        <p className="text-theme-100 leading-relaxed">拥有100+技术专家，为您提供专业咨询</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <Clock className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-lg">快速响应</h4>
                        <p className="text-theme-100 leading-relaxed">24小时内响应，快速解决您的问题</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <CheckCircle className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-lg">定制方案</h4>
                        <p className="text-theme-100 leading-relaxed">根据您的需求量身定制解决方案</p>
                      </div>
                    </div>
                    
                    <div className="flex items-start">
                      <div className="w-10 h-10 bg-white/20 backdrop-blur-sm rounded-xl flex items-center justify-center mr-4 mt-1 flex-shrink-0">
                        <CheckCircle className="h-5 w-5" />
                      </div>
                      <div>
                        <h4 className="font-semibold mb-2 text-lg">长期支持</h4>
                        <p className="text-theme-100 leading-relaxed">提供全程技术支持和服务保障</p>
                      </div>
                    </div>
                  </div>

                  <div className="mt-12 p-6 bg-white/10 backdrop-blur-sm rounded-2xl border border-white/20">
                    <h4 className="font-semibold mb-4 text-lg">联系方式</h4>
                    <div className="space-y-3">
                      <div className="flex items-center">
                        <Phone className="h-4 w-4 mr-3 text-secondary-300" />
                        <span className="text-theme-100">0731-81877778</span>
                      </div>
                      <div className="flex items-center">
                        <Mail className="h-4 w-4 mr-3 text-secondary-300" />
                        <span className="text-theme-100"><EMAIL></span>
                      </div>
                      <div className="flex items-center">
                        <MapPin className="h-4 w-4 mr-3 text-secondary-300" />
                        <span className="text-theme-100">湖南省长沙市岳麓区尖山湖路2号</span>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>
    </div>
  );
};

export default ContactForm;




