import React from 'react';
import { useTheme } from '../context/ThemeContext';

interface BackgroundDecorationsProps {
  particleCount?: number;
  showGradients?: boolean;
  particleOpacity?: number;
  className?: string;
}

const BackgroundDecorations: React.FC<BackgroundDecorationsProps> = ({
  particleCount = 15,
  showGradients = true,
  particleOpacity = 60,
  className = ''
}) => {
  const { theme } = useTheme();

  // Generate floating particles
  const particles = Array.from({ length: particleCount }, (_, i) => ({
    id: i,
    size: Math.random() * 5 + 2,
    left: Math.random() * 100,
    top: Math.random() * 100,
    delay: Math.random() * 4,
    duration: Math.random() * 3 + 4,
  }));

  return (
    <div className={`absolute inset-0 ${className}`}>
      {/* Background Gradients */}
      {showGradients && (
        <>
          <div className="absolute top-0 left-0 w-96 h-96 bg-gradient-to-br from-secondary-500/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-96 h-96 bg-gradient-to-tl from-theme-600/20 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </>
      )}
      
      {/* Floating Particles */}
      {particles.map((particle) => (
        <div
          key={particle.id}
          className={`particle absolute rounded-full opacity-${particleOpacity}`}
          style={{
            width: `${particle.size}px`,
            height: `${particle.size}px`,
            left: `${particle.left}%`,
            top: `${particle.top}%`,
            animationDelay: `${particle.delay}s`,
            animationDuration: `${particle.duration}s`,
            background: particle.id % 2 === 0 ? '#C5AA89' : (theme === 'blue' ? '#0077B6' : '#790606'),
          }}  
        />
      ))}
    </div>
  );
};

export default BackgroundDecorations;