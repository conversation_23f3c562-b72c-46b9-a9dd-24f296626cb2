import React from 'react';
import { Link } from 'react-router-dom';
import ReactPlayer from 'react-player';
import {
  ArrowR<PERSON>,
  MessageSquare,
  Brain,
  Code,
  Cloud,
  FileText,
  CheckCircle
} from 'lucide-react';
import PinnacleDemo from '@/assets/pinnacle-demo.mp4';

const OptPinnacleDetail: React.FC = () => {
  return (
    <div className="relative overflow-hidden">
      {/* Banner部分 */}
      <section className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white pt-24 pb-16 relative overflow-hidden">
        {/* 简约背景装饰 */}
        <div className="absolute inset-0">
          <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
          <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
        </div>

        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 relative z-10">
          <div className="text-center pt-10">
            <h1 className="text-4xl lg:text-5xl font-bold mb-6">
              OptPinnacle问鼎 - 您的AI辅助建模专家
            </h1>
            <p className="text-xl text-gray-300 mb-8 max-w-3xl mx-auto leading-relaxed">
              让AI为您构建完美的优化模型
            </p>

            <div className="flex flex-col sm:flex-row gap-4 justify-center">
              <Link
                to={"#"}
                target="_blank"
                className="group inline-flex items-center justify-center px-8 py-4 bg-white text-theme-900 font-medium rounded-lg hover:bg-gray-100 transition-all duration-300 shadow-lg"
              >
                <span>立即试用</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </div>
          </div>
        </div>
      </section>

      {/* OptPinnacle介绍 */}
      <section className="py-16 bg-white">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">新一代决策智能平台--OptPinnacle</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          </div>

          <div className="grid grid-cols-1 lg:grid-cols-2 gap-12 items-center">
            {/* 左侧介绍文字 */}
            <div>
              <p className="text-lg text-gray-600 leading-relaxed mb-6">
                OptPinnacle--企业级智能优化平台。平台高性能云计算引擎，支持用户以自然语言描述业务需求，系统自动完成问题抽象、模型构建、代码生成、云端求解与可视化报告输出，全面覆盖 MILP、VRP等主流优化场景。
              </p>

              <div className="space-y-4">
                <div className="flex items-center text-gray-600">
                  <CheckCircle className="h-5 w-5 mr-3 text-green-500" />
                  <span>自然语言描述，AI自动建模</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <CheckCircle className="h-5 w-5 mr-3 text-green-500" />
                  <span>云端高性能计算引擎</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <CheckCircle className="h-5 w-5 mr-3 text-green-500" />
                  <span>零代码快速上线</span>
                </div>
                <div className="flex items-center text-gray-600">
                  <CheckCircle className="h-5 w-5 mr-3 text-green-500" />
                  <span>内置丰富行业模板</span>
                </div>
              </div>
            </div>

            {/* 右侧视频 */}
            <div className="relative">
              <div className="h-full bg-gradient-to-br from-theme-50 to-secondary-50 rounded-2xl p-2 shadow-lg">
                <ReactPlayer
                  src={PinnacleDemo}
                  controls
                  width="100%"
                  height="100%"
                />
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* 优势 */}
      <section className="py-16 bg-gray-50">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold text-gray-900 mb-4">产品优势</h2>
            <div className="w-16 h-1 bg-theme-600 mx-auto mb-6 rounded-full" />
          </div>

          <div className="space-y-12">
            {/* 自然语言，即刻建模 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl">
                    <MessageSquare className="h-6 w-6" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">自然语言，即刻建模</h3>
                  <p className="text-gray-600 leading-relaxed">
                    无需专业建模背景，用您最熟悉的语言描述问题！OptPinnacle智能分析您的自然语言描述，精准提取并识别出决策变量、约束条件、目标函数和关键参数，让建模如同对话般自然流畅。
                  </p>
                </div>
              </div>
            </div>

            {/* 智能构建，模型无忧 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl">
                    <Brain className="h-6 w-6" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">智能构建，模型无忧</h3>
                  <p className="text-gray-600 leading-relaxed">
                    告别手动公式推导！基于强大的AI推理能力，OptPinnacle能够准确无误地构建数学优化模型。无论是混合整数线性规划（MILP）、车辆路径问题（VRP）、作业车间调度（JOBSHOP），还是其他复杂场景，我们都能为您提供坚实基础，并支持灵活扩展。
                  </p>
                </div>
              </div>
            </div>

            {/* 代码生成，智能纠错 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl">
                    <Code className="h-6 w-6" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">代码生成，智能纠错</h3>
                  <p className="text-gray-600 leading-relaxed">
                    一步到位，代码即用！OptPinnacle不仅能自动生成高质量的Python和MiniZinc代码，并支持多种主流求解器。更令人惊喜的是，我们内置了智能纠错引擎，能在测试过程中根据错误信息自动诊断并修正代码，大幅提升开发效率，确保模型顺利运行。
                  </p>
                </div>
              </div>
            </div>

            {/* 云端算力，瞬息求解 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl">
                    <Cloud className="h-6 w-6" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">云端算力，瞬息求解</h3>
                  <p className="text-gray-600 leading-relaxed">
                    本地计算瓶颈？不再是问题！OptPinnacle提供强大的云计算资源支持，让您的复杂优化模型在云端高效执行。无论问题规模大小，我们都能为您提供稳定、快速的求解服务，助您在瞬息万变的市场中抢占先机。
                  </p>
                </div>
              </div>
            </div>

            {/* 模板案例，加速创新 */}
            <div className="bg-white rounded-2xl p-8 shadow-lg">
              <div className="flex items-start space-x-4">
                <div className="flex-shrink-0">
                  <div className="inline-flex items-center justify-center w-12 h-12 bg-theme-50 text-theme-600 rounded-xl">
                    <FileText className="h-6 w-6" />
                  </div>
                </div>
                <div>
                  <h3 className="text-xl font-bold text-gray-900 mb-3">模板案例，加速创新</h3>
                  <p className="text-gray-600 leading-relaxed">
                    从零开始太慢？我们为您加速！OptPinnacle内置了丰富的各类问题案例模板，涵盖物流、生产、排班等多个行业场景。您可以直接选用或在此基础上进行修改，快速启动您的优化项目，激发无限创新潜能。
                  </p>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

    </div>
  );
};

export default OptPinnacleDetail;
