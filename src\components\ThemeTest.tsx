import React, { useContext } from 'react';
import { ThemeContext } from '@/context/ThemeContext';

const ThemeTest: React.FC = () => {
  const { theme, toggleTheme } = useContext(ThemeContext);
  
  return (
    <div className="p-8 flex flex-col items-center space-y-6">
      <h2 className="text-2xl font-bold">主题测试</h2>
      
      <div className="flex space-x-4">
        <div className="p-4 bg-theme-50 text-theme-900 border border-theme-200 rounded-lg">
          主题色 50-900
        </div>
        <div className="p-4 bg-theme-100 text-theme-800 border border-theme-300 rounded-lg">
          主题色 100-800
        </div>
        <div className="p-4 bg-theme-200 text-theme-700 border border-theme-400 rounded-lg">
          主题色 200-700
        </div>
        <div className="p-4 bg-theme-300 text-theme-600 border border-theme-500 rounded-lg">
          主题色 300-600
        </div>
      </div>
      
      <div className="flex space-x-4">
        <div className="p-4 bg-primary text-white rounded-lg">
          主色
        </div>
        <div className="p-4 bg-primary-light text-white rounded-lg">
          主色-浅色
        </div>
        <div className="p-4 bg-primary-dark text-white rounded-lg">
          主色-深色
        </div>
        <div className="p-4 bg-accent text-white rounded-lg">
          强调色
        </div>
      </div>
      
      <div className="flex space-x-4">
        <div className="p-4 bg-gradient-primary text-white rounded-lg">
          主色渐变
        </div>
        <div className="p-4 shadow-primary rounded-lg p-4">
          主色阴影
        </div>
      </div>
      
      <button 
        onClick={toggleTheme}
        className="px-6 py-3 bg-theme-600 text-white rounded-lg hover:bg-theme-700 transition-colors"
      >
        当前主题: {theme} (点击切换)
      </button>
    </div>
  );
};

export default ThemeTest;