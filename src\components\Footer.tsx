import React from 'react';
import { Link } from 'react-router-dom';
import { Mail, Phone, MapPin, Building2, ArrowRight } from 'lucide-react';

const Footer: React.FC = () => {

  return (
    <footer className="bg-gradient-to-br from-theme-900 via-theme-800 to-secondary-900 text-white relative overflow-hidden">
      {/* 简约背景装饰 */}
      <div className="absolute inset-0">
        <div className="absolute top-0 left-0 w-64 h-64 bg-gradient-to-br from-theme-600/10 to-transparent rounded-full -translate-x-1/2 -translate-y-1/2" />
        <div className="absolute bottom-0 right-0 w-64 h-64 bg-gradient-to-tl from-secondary-600/10 to-transparent rounded-full translate-x-1/2 translate-y-1/2" />
      </div>

      <div className="relative z-10 max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 pt-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8 lg:gap-12">
          {/* 公司信息 */}
          <div className="lg:col-span-2">
            <div className="flex items-center gap-3 mb-6">
              <div className="p-2 bg-white/10 rounded-lg">
                <Building2 className="h-5 w-5 text-theme-300" />
              </div>
              <h3 className="text-lg font-bold text-white">
                北京大学长沙计算与数字经济研究院
                <p>工业智能研究中心</p>
              </h3>
            </div>
            <p className="text-gray-300 mb-6 leading-relaxed max-w-md">
              专注于智能优化技术，为企业提供数据驱动的决策解决方案，助力企业数智化转型升级。
            </p>
          </div>

          {/* 快速链接 */}
          <div>
            <h3 className="text-base font-semibold mb-6 text-white">快速链接</h3>
            <ul className="space-y-3">
              {[
                { name: 'OptSuite', href: '/products/optsuite' },
                { name: 'OptAps', href: '/products/optsuite' },
                { name: '文档', href: '/documentation' },
                { name: '关于我们', href: '/about' }
              ].map((link, index) => (
                <li key={index}>
                  <Link
                    to={link.href}
                    className="text-gray-300 hover:text-white transition-colors duration-300 flex items-center gap-2 group text-sm"
                  >
                    <ArrowRight className="w-0 h-3 mr-0 opacity-0 group-hover:opacity-100 group-hover:w-3 group-hover:translate-x-1 transition-all duration-300" />
                    <span className="group-hover:translate-x-1 transition-transform duration-300">
                      {link.name}
                    </span>
                  </Link>
                </li>
              ))}
            </ul>
          </div>

          {/* 联系信息 */}
          <div>
            <h3 className="text-base font-semibold mb-6 text-white">联系我们</h3>
            <div className="space-y-4">
              {[
                { icon: <Mail className="h-4 w-4" />, text: '<EMAIL>', type: 'email' },
                { icon: <Phone className="h-4 w-4" />, text: '0731-81877778', type: 'phone' },
                { icon: <MapPin className="h-4 w-4" />, text: '湖南省长沙市岳麓区尖山湖路2号', type: 'address' }
              ].map((contact, index) => (
                <div key={index} className="flex items-start gap-3 text-gray-300 group">
                  <div className="p-1.5 bg-white/10 rounded-md group-hover:bg-white/20 transition-colors duration-300 mt-0.5">
                    {contact.icon}
                  </div>
                  <span className="group-hover:text-white transition-colors duration-300 text-sm leading-relaxed">
                    {contact.text}
                  </span>
                </div>
              ))}
            </div>
          </div>
        </div>

        {/* 底部版权 */}
        <div className="border-t border-white/10 mt-12 pt-6 pb-6">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="text-gray-400 text-sm">
              版权所有 © 2025 北京大学长沙计算与数字经济研究院
            </div>
            <div className="text-gray-400 text-sm">
              专注智能优化 · 驱动数字未来
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
};

export default Footer;